# Skills System

*Domain-specific guide for SellSword v4 skills framework*

*The foundation for how sellswords develop their capabilities*

## Overview

The SellSword v4 skills system uses a three-tier structure designed to create dramatically different playstyles through mechanical innovation rather than simple dice bonuses.

### Design Philosophy

- **Enhancement, Not Gating** - Skills improve your capabilities, they don't **grant** new ones
- **Core Skills** provide foundational competence and dice bonuses
- **Techniques** dramatically alter how you approach actions, creating unique playstyles
- **Masteries** represent deep expertise with specific tools, equipment, or applications
- **Mechanical Innovation** over simple number increases
- **Resource Flexibility** - skills enable novel uses of Stamina and Will
- **Equipment Synergy** - advanced skills interact meaningfully with gear

---

## Tier 1: Core Skills (Foundation)

*Cost: Level 1 = 1 SP, Level 2 = 2 additional SP, Level 3 = 3 additional SP*

These 15 skills form the foundation of character capability, adding dice to actions and providing secondary benefits.

### Physique Skills
- **Fortitude** - Endurance, resisting physical effects *(+1 Stamina per level)*
- **Heavy Melee** - Heavy weapons and shields, forceful combat
- **Might** - Physical power, lifting, breaking, brawling *(+1 Carry per level)*

### Finesse Skills
- **Mobility** - Movement, dodging, acrobatics *(+1 Speed per level)*
- **Light Melee** - Light weapons and shields, finesse combat
- **Stealth** - Moving unseen, hiding 

### Precision Skills
- **Subtlety** - Lockpicking, traps, sleight of hand
- **Ranged** - Bows, crossbows, thrown weapons *(+1 Range per level)*
- **Weave** - Shaping magical effects *(requires Wyrd Lore)*

### Focus Skills
- **Observe** - Searching, tracking, noticing details 
- **Resolve** - Mental fortitude, resisting mental effects *(+1 Will per level)*
- **Evoke** - Gathering magical power *(requires Wyrd Lore)*

### Insight Skills
- **Reason** - Logic, investigation, problem-solving *(+1 Lore Slot per level)*
- **Influence** - Persuasion, intimidation, leadership *(+1 Standing per level)*
- **Scry** - Magical perception, divination *(requires Wyrd Lore)*

### Wager System

Before rolling any skill check, you may **wager** resources to add bonus dice to your pool:

#### Green Skills - Stamina Wager
**Skills:** Might, Heavy Melee, Fortitude, Light Melee, Mobility
- Spend Stamina points to add dice (1 Stamina = +1 die)
- If you roll any **1s** on wagered dice, they are lost

#### Yellow Skills - Time Wager
**Skills:** Stealth, Subtlety, Ranged, Weave, Observe
- Spend additional Action Points for bonus dice (1 AP = +2 dice)
- No risk of loss - represents taking extra time for precision

#### Blue Skills - Will Wager
**Skills:** Evoke, Resolve, Influence, Scry, Reason
- Spend Will points to add dice (1 Will = +1 die)
- If you roll any **1s** on wagered dice, they are lost

---

## Tier 2: Techniques (Playstyle Differentiators)

*Cost: 2 SP each*

Techniques fundamentally change how you approach actions, creating distinct playstyles through unique mechanics. They focus on allowing players to do things better and faster than standard approaches.

### Technique Design Principles
- **Mechanical Innovation** - Provide unique capabilities, not dice bonuses
- **Resource Flexibility** - Enable novel uses of Stamina, Will, and AP
- **Equipment Synergy** - Interact meaningfully with gear and equipment
- **Playstyle Creation** - Make characters feel dramatically different in play

### Prerequisites
- Techniques may require specific Core Skills, other Techniques, or character attributes
- Prerequisites should support the technique's theme and mechanical requirements
- Some techniques may have flexible prerequisites (e.g., "Mobility Level 1 OR Light Melee Level 1")

#### Juggernaut (Technique)
*Prerequisite: Might Level 1*
*Cost: 2 SP*

You are unstoppable in close-quarters combat, using your sheer force to break through defenses.

- **Stunt Enhancement: Gain Momentum:** Whenever you gain momentum you also gain the Upper Hand on your next Physique-based action. This effect is stackable until you take an action other than Physique or don't wager at least 2 Stamina.
- **Unstoppable:** Whenever you defeat or compromise an opponent, you may spend 1 Stamina to immediately take one additional Physique-based action with an AP cost of 1.

#### Two-Handed (Technique)
*Prerequisite: Heavy Melee Level 1*
*Cost: 2 SP*

You specialize in wielding heavy weapons with both hands for maximum power and control.

- **Stunt Enhancement: Apply Force:** When sundering armor, you deal penetrating damage equal to the sundered amount.
- **Cleave:** When you make a penetrating attack, you may spend 1 Stamina to Cleave. Make a second attack roll against a different adjacent enemy within your reach at a -2 dice penalty. If successful, this second attack deals damage equal to your weapon's base damage, up to its Carry value.

#### Defender (Technique)
*Prerequisite: Fortitude Level 1*
*Cost: 2 SP*

You excel at defensive combat, holding ground and using your weapons and armor to protect yourself and others.

- **Stunt Enhancement: Second Wind:** You may use Second Wind while wagering stamina but must give the recovered Stamina to an adjacent ally instead of yourself.
- **Stalwart Guard:** Wager at least 2 Stamina to reduce block AP cost by 1.

#### Dual Wielder (Technique)
*Prerequisite: Light Melee Level 1*
*Cost: 2 SP*

You fight with a weapon in each hand, creating a whirlwind of coordinated attacks.

- **Stunt Enhancement: Flowing Strike:** You may use Flowing Strike on any melee attack, reducing the cost from 2 to 1 extra success and removing the -2 dice penalty for the off-hand attack.
- **Weapon Combination:** When wielding two weapons in an action they are both effective, you may spend 1 Stamina to have both of your equipment dice succeed on a 4-6, as if you had Mastery.

#### Skirmisher (Technique)
*Prerequisite: Mobility Level 1*
*Cost: 2 SP*

You excel at mobile, hit-and-run tactics, using speed and positioning to your advantage.

- **Stunt Enhancement: Reposition:** You may use the Reposition stunt on any action, also gaining the Upper Hand (+2 dice) on your next attack.
- **Evasive Dodge:** Wager at least 2 Stamina to reduce dodge AP cost to 0.

#### Duelist (Technique)
*Prerequisite: Light Melee Level 1*
*Cost: 2 SP*

You specialize in precise one-on-one combat with finesse weapons, emphasizing defense and counterattacks.

- **Stunt Enhancement: Flowing Strike:** When you successfully parry an attack you may purchase the Flowing Strike stunt for 1 success instead of 2 and removing the -2 dice penalty.
- **Void Dodge:** You may take a -2 dice penalty to Dodge and if successful you gain the Upper Hand on your next attack against that target.

#### Marksman (Technique)
*Prerequisite: Ranged Level 1*
*Cost: 2 SP*

You excel at precision ranged combat, making difficult shots and overcoming obstacles.

- **Stunt Enhancement: Target Weakness:** When using the Target Weakness stunt with ranged weapons, reduce target's effective DR by 2 instead of 1.
- **Steady Aim:** When you Take Time on a ranged attack, you may also reduce Complexity by 1.

## Tier 3: Masteries (Deep Specialization)

---

## Tier 3: Masteries (Deep Specialization)

*Cost: 3 SP each*

Masteries represent deep expertise with specific tools, techniques, or applications. They provide significant mechanical benefits for focused character builds.

### Mastery Design Principles
- **Deep Specialization** - Focused on specific equipment, environments, or applications
- **Significant Investment** - Higher cost reflects substantial character commitment
- **Advanced Prerequisites** - Usually require Core Skills Level 2 or specific Techniques
- **Meaningful Benefits** - Provide capabilities that justify the investment

---

## Character Creation

### Starting Resources
- **7 Skill Points** to distribute among Core Skills and Techniques/Masteries
- **Recommended Builds:**
  - Core Skill Level 3 (6 SP) + 1 SP remaining
  - Core Skill Level 2 (3 SP) + Technique (2 SP) + 2 SP remaining
  - Core Skill Level 1 (1 SP) + 2 Techniques (4 SP) + 2 SP remaining
  - Multiple Core Skills at Level 1 for broad competence

### Advancement
- Gain additional Skill Points through play and training
- Improve existing Core Skills or learn new Techniques/Masteries
- Prerequisites must be met before purchasing advanced options

---

## Integration with Other Systems

### Combat System
- **Action Types:** Skills modify Physique/Finesse/Precision/Focus/Insight actions
- **Resource Costs:** Skills may cost Stamina or Will to use
- **Equipment Synergy:** Skills enhance equipment effectiveness
- **Tactical Options:** Skills provide new combat maneuvers

### Equipment System
- **Weapon Skills:** Enhance equipment effectiveness
- **Combat Techniques:** Modify how equipment works
- **Synergy Bonuses:** Advanced skills unlock equipment potential

### Magic System
- **Wyrd Lore:** Required for magical abilities
- **Magic Skills:** Evoke, Weave, Scry use Focus/Precision/Insight
- **Will Costs:** Magic depletes mental resources
- **Mishaps:** Failed magic can cause complications

---

## Domain Context

### Working in Skills Domain

**Before Making Changes:**
1. **Read the Style Guide:** [SellSword_v4_Style_Guide.md](../SellSword_v4_Style_Guide.md)
2. **Check Current Priorities:** [SellSword_v4_TODO.md](../SellSword_v4_TODO.md)
3. **Review Integration Points:** How changes affect Combat, Equipment, and Character Creation
4. **Consider Balance:** Skill power level vs. cost and prerequisites

### Key Formatting Standards

**Language Guidelines:**
- Use "When you..." instead of "You gain the ability to..."
- Use "You may..." instead of "You can now..."
- Imply actions were always possible, skills just make them better
- Avoid "granting" language - skills enhance, don't create

**Skill Descriptions:**
```markdown
#### **[Skill Name]** ([Action Type])
*Prerequisite: [Requirement]*

[Description of skill's purpose and theme]

- **[Mechanical Benefit]:** [Clear description of game effect]
- **[Resource Use]:** [How skill interacts with Stamina/Will]
- **[Integration]:** [How skill works with equipment/other systems]
```

### Terminology Standards

**Skill Terms:**
- **Core Skills:** Basic abilities (15 total)
- **Techniques:** Specialized applications of core skills
- **Masteries:** Advanced capabilities requiring multiple prerequisites
- **Skill Points (SP):** Currency for advancement

**Resource Terms:**
- **Stamina:** Physical energy for actions
- **Will:** Mental energy for focus and magic
- **Action Points (AP):** Turn-based action economy

---

## Cross-References

### Related Systems
- **Combat Mechanics:** See [Combat README](../Combat/README.md) for skill applications in combat
- **Equipment:** See [Equipment README](../Equipment/README.md) for skill-equipment interactions
- **Character Creation:** See [Characters README](../Characters/README.md) for skill selection and advancement
- **Magic System:** See [Magic README](../Magic/README.md) for magical skill requirements

### Skills Integration
- **Action Enhancement:** Skills add dice to relevant actions
- **Resource Management:** Skills provide new ways to use Stamina and Will
- **Equipment Synergy:** Skills enhance equipment effectiveness
- **Tactical Options:** Skills provide new approaches to challenges

---

*This framework provides the foundation for creating distinct sellsword archetypes while maintaining tactical depth and meaningful choices.*

**Version:** 4.0 (Streamlined)
**Status:** Core framework ready for technique/mastery development
