# Combat System

> **Combat in 5 Steps**
> 1. Declare Target & Hit Location
> 2. Build Pool: Action + Skill + Equipment Dice (2) + Wager - Difficulty
> 3. Apply Modifiers (+/- dice)
> 4. Roll & Count Successes
> 5. Resolve Outcome & Spend Extra Successes (XS)

*Detailed guide to SellSword's tactical combat mechanics*

## Table of Contents

[[#1. Philosophy]]
[[#2. Flow]]
[[#3. Resources]]
[[#4. Resolution]]
[[#5. Damage & Wounds]]
[[#6. Extra Success (XS)]]
[[#7. Examples]]

---

## 1. Philosophy

**Quick, Lethal, Tactical.** SellSword combat emphasizes **tactical decision-making** over statistical optimization. Key principles:

- **Equipment Matters:** Gear provides decisive advantages
- **Positioning Counts:** Flanking, cover, and reach create tactical opportunities
- **Resources Are Limited but Powerful:** Stamina and Will expenditure turn the tide
- **Wounds Have Consequences:** Damage creates lasting mechanical effects
- **Meaningful Choices:** Every action has clear trade-offs and consequences
- **Narrative over Mechanics:** Rules should never trump verisimilitude


---

## 2. Rounds and Turns

No Initiative. Each combat **Round** players have (3) **Action Points** to declare
- **Attack/Block/Parry:** Weapon's Carry value (min 1 - 3)
- **Dodge:** 1 AP
- **Move up to your Speed:** 1 AP
- **Other Actions:** Typically 1 AP (use item, pull lever, etc.)

These actions occur in a **Turn** as follows:
### Declarative Phase
- 1st GM declares all NPC actions
- 2nd Players declare character actions in response, encouraged to discuss and collaborate
- All actions are committed before resolution begins

### Active Phase
- GM resolves actions in order they determine best.
- Typically lowest AP, quickest, first in order Melee -> Ranged -> Move -> Magic
- Similar and opposed actions occur simultaneously

**Turns** are repeated until all **AP** is spent and the next **Round** begins

---
## 3. Resources

Combat in SellSword revolves around the strategic expenditure of resources to control the ebb and flow of battle. Wager a resource to improve your odds adding dice to your action pool at risk of loss. Spend a resource to push past your limits performing Heroic Actions.

**Stamina** = Physique + Fortitude - Fatigue
Use on **Green Skills:** Might, Heavy Melee, Fortitude, Light Melee, Mobility
+1 dice per SP, lose any rolled 1's

**Will** = Focus + Resolve - Strain
Use on **Blue Skills:** Evoke, Resolve, Influence, Scry, Reason
+1 dice per WP, lose any rolled 1's
#### Heroic Actions

**Bend the Rules:** Stamina and Will represent more than just mechanical resources - they're your character's ability to push beyond normal limits and perform heroic feats when it matters most. Players should feel encouraged to ask "Can I spend Stamina/Will to..." when they want to attempt something just outside the normal rules. GMs should generally say yes, but at a cost.

**Common Heroic Actions:**
- **Rush a Defense:** "Can I spend 1 Stamina to block this fast attack with my slow weapon?" *(Allow the block but at Minor Difficulty -2)*
- **Extra Movement:** "Can I spend 1 Stamina to reach the fight this round?" *(Allow a few extra hexes of movement)*
- **Automatic Success:** "This Resolve check is crucial - can I spend 1 Will to just succeed?" *(Skip the roll entirely)*
- **Push Through Pain:** "Can I ignore this wound penalty for something important?" *(Spend 1 Stamina to negate penalties for one action)*

**GM Guidelines:**
- **Say Yes, But...** Default to allowing heroic actions with appropriate resource costs
- **Match the Stakes:** Bigger asks require bigger costs or additional penalties
- **Maintain Drama:** Don't let resource spending trivialize important moments
- **Be Consistent:** Similar requests should have similar costs

### Resource Recovery

#### Taking a Breather
- **Cost:** 2 AP's
- **Effect:** Regain 1 Stamina immediately
- **Tactical Use:** Emergency Stamina recovery when resources are critically low

#### Second Wind XS
- **Cost:** 1 extra success from a Physique-based action not wagering stamina
- **Effect:** Regain 1 spent Stamina
- **Advantage:** Doesn't cost AP, can be combined with other actions

**Stamina:** Returns to full when the party is "safe" or not in imminent danger for at least a minute. Encourage tactical retreats. Barring a door. Scaling a cliff.

**Will:** While on contract can recover points by GM discretion, typically from significant victories, exceptional character moments, or overcoming adversity. It is fully recovered after a night in comfort

---

## 4. Resolution

### Step 1: Declare Target & Hit Location

**Hit Location Target Difficulty (TD) Modifier:**
- **Body:** TD +0 (torso, limbs) - easiest to hit, minor wound, subject to full armor
- **Voids:** TD +2 (armor gaps, hands, head) - major wound, subject to minor armor
- **Vitals:** TD +4 (eyes, heart) - grievous wound, bypasses armor
### Step 2: Build Dice Pool

**Base Pool:** Action + Skill + Equipment Dice (always 2) + Wager - Difficulty

**Action Selection:**
- **Physique:** Forceful attacks, Allows Physique XS
- **Finesse:** Quick attacks, Allows Finesse XS
- **Precision:** accurate attacks, Allows Precision XS

### Step 3: Apply Combat Modifiers

**Upper Hand (+2 dice):**
- Flanking opponent
- Assistance from allies
- Advantageous positioning
- Superior equipment quality

**Compromised (-2 dice):**
- Being flanked
- Off-balance or prone
- Disadvantageous positioning
- Environmental penalties

### Step 4: Roll & Apply Results

**Success Threshold:** 1+ successes hits the target
**Extra Successes (XS):** XS options granted by Actions, Skill, or Gear for additional effects

**Common XS Effects:**
- **Extra Damage (PHY):** Upgrade wound 1 tier
- **Extra Attack (FIN):** Make another (1AP) attack at minor difficulty (-2)
- **Extra Penetration (PRC):** Lower targets armor 1 tier

---

### Defense Mechanics

#### Passive Defense

**Target Difficulty (TD):** Automatic defense representing agility and size
- **Formula:** 0 + AGI + Size modifier
- **Applied as Difficulty dice** to all attacks against the character
- **Always active** - no AP cost required

#### Active Defense Options

#### Dodge (1 AP)
- **Pool:** Finesse + Mobility + Stamina
- **Opposed Roll:** Your successes vs. attacker's successes
- **Success:** Completely negates the attack
- **Failure:** Attack hits normally

#### Parry (Weapon Carry AP)
- **Pool:** Finesse + Light Melee + Stamina
- **Opposed Roll:** Your successes vs. attacker's successes
- **Success:** Completely negates the attack
- **Failure:** Attack hits normally

#### Block (Shield Carry AP)
- **Pool:** Physique + Heavy Melee + Stamina
- **Opposed Roll:** Your successes vs. attacker's successes
- **Success:** Completely negates the attack
- **Failure:** Attack hits normally
- **Equipment Requirement:** Must have shield ready

### Defense Strategy

**Dodge:** Best for lightly armored, high-Agility characters
**Parry:** Effective with high-Parry weapons, allows counterattacks
**Block:** Maximum protection with shields, can protect others
**Passive Defense:** Always active unless restrained, enhanced by high Agility

---

## 5. Damage & Wounds

### Damage Resolution Process

#### 1. Calculate Base Damage
Use weapon's Damage value (typically 1-3)

#### 2. Apply Pierce
Weapon's Pierce reduces target's effective Damage Reduction
- **Effective DR = Target DR - Weapon Pierce**
- **Minimum DR = 0** (Pierce cannot create negative DR)

#### 3. Determine Penetrating Damage
- **Penetrating Damage = Weapon Damage - Effective DR**
- **If Penetrating Damage ≤ 0:** No wound, attack deflected
- **If Penetrating Damage > 0:** Roll wound dice

#### 4. Roll Wound Dice
- **Roll d6s equal to Penetrating Damage**
- **Consult appropriate Wound Table** for hit location
- **Apply wound effects immediately**
### Damage Examples

#### Player vs. Player/Monster Damage
**Scenario:** Longsword (Damage 2, Pierce 1) vs. Chainmail Hauberk (Body DR 2)
1. **Effective DR:** 2 - 1 = 1
2. **Penetrating Damage:** 2 - 1 = 1
3. **Wound Roll:** Roll 1d6, consult Body Wound Table

**Scenario:** Same attack vs. Gambeson (Body DR 1)
1. **Effective DR:** 1 - 1 = 0
2. **Penetrating Damage:** 2 - 0 = 2
3. **Wound Roll:** Roll 2d6, consult Body Wound Table

#### Monster vs. Player Damage
**Scenario:** Monster Bite (Damage 3) vs. Player in Chainmail Hauberk (Body DR 2)
1. **Effective DR:** 2 - 0 = 2 (no Pierce)
2. **Penetrating Damage:** 3 - 2 = 1
3. **Player Wound:** Roll 1d6, consult Body Wound Table


| d6  | Body      | Voids  | Vitals    |
| --- | --------- | ------ | --------- |
| 1   | Left Leg  | Knees  | Achiles   |
| 2   | Right Leg | Groin  | Hamstring |
| 3   | Chest     | Wrists | Gut       |
| 4   | Shoulder  | Armpit | Heart     |
| 5   | Left Arm  | Collar | Throat    |
| 6   | Right Arm | Head   | Eyes      |
### Player vs. Monster Damage Mechanics

**When Players Attack Monsters:**
- Players use weapon damage values (typically 1-3)
- Monsters have tiered wound pools (Minor, Major, Grievous, Deadly)
- Penetrating damage fills wound pools rather than rolling on tables
- Monsters don't roll wound dice - they take wounds directly

**When Monsters Attack Players:**
- Monsters use standardized damage scale (1-6)
- Players roll wound dice equal to penetrating damage
- Players consult wound tables for specific effects
- Wound severity depends on dice results, not damage amount

**Key Differences:**
- **Monster Resilience:** Wound pools vs. wound table rolls
- **Damage Scaling:** Monster attacks can deal higher base damage (up to 6)
- **Effect Application:** Direct wound accumulation vs. randomized wound effects
- **Simplification:** Monsters avoid complex wound tracking for GM ease


---

## 6. Extra Success (XS)

### Overview

Stunts are minor, immediate advantages or narrative flourishes purchased using **extra successes** rolled on a successful action check. They represent the character's ability to capitalize on exceptional performance, turning a good result into something spectacular.

### Core Mechanics

- **Earning:** Extra successes are any 6s rolled beyond the minimum required for the action
- **Spending:** Declared immediately after a successful roll, before damage/effects are resolved
- **Cost:** Most stunts cost 1 extra success unless noted otherwise
- **Stacking:** Multiple stunts can be purchased if enough extra successes are available
- **Association:** Available stunts are tied to the Action used for the roll

### Design Philosophy

- **Tactical Enhancement:** Stunts provide meaningful tactical choices without overwhelming complexity
- **Resource Integration:** Work seamlessly with v4's Stamina/Will system and equipment stats
- **Skill Amplification:** Skills enhance stunts but never gatekeep access to basic versions
- **Clear Mechanics:** All effects have explicit duration and mechanical impact

### Universal Stunts

*Available regardless of which Action was used*

#### **Impressive Display**
- **Cost:** 1 extra success
- **Effect:** Gain +2 dice on your next social interaction check (Influence, intimidation, etc.) against anyone who witnessed this action
- **Duration:** Until used or end of scene

### Extra Success (XS) Options

These XS options are available with any action that meet the additional requirements. Attack XS are stackable up to your weapon's Carry.

#### Physique Actions

**Powerful Strike**
- Cost: 1XS
- Requirement: Heavy Melee Attack
- Effect: upgrade wound 1 tier

**Second Wind**
- Cost: 1XS
- Requirement: Can **NOT** be used when wagering stamina
- Effect: Regain 1 spent Stamina

**Move**
- Cost: 1XS
- Requirement: Heavy Melee OR Might Action
- Effect: Displace target (Strength + Might + Weapon Carry - Size) hexes, Fortitude Resist

**Overwhelm**
- Cost: 2XS
- Requirement: Heavy Melee or Might Action
- Effect: Target is compromised until they spend 1AP to right themselves, Fortitude Resist

#### Finesse

**Reposition**
- Cost: 1XS
- Requirement: Light Melee Attack OR Mobility
- Effect: Move up to your AGI score in hexes as part of this action

**Evasive Action**
- Cost: 1XS
- Requirement: Light Melee Attack Or Mobility
- Effect: Gain the Upper Hand on your next defense roll (dodge, parry, or block)
- Duration: Until your next defense attempt this round

**Feint**
- Cost: 1XS
- Requirement: Light Melee Attack and target defends
- Effect: No wounding, instead gain Upper Hand (+2 dice) on your next attack against this target

**Flowing Strike**
- Cost: 2XS
- Requirement: Light Melee Attack
- Effect: Make an immediate additional attack at -2 dice penalty

#### Precision

**Find the Gap**
- Cost: 1XS
- Requirement: Light Melee OR Ranged Attack
- Effect: Lower Targets Armor 1 Tier for this attack

**Pinpoint Shot**
- **Cost:** 1 extra success
- **Effect:** You have the "Upper Hand" on your next ranged attack against the target

**Disrupt**
- **Cost:** 2 extra successes
- **Effect:** Target loses 1 AP

---


---

## 7. Examples

### Example 1: Sword vs. Shield

**Participants:**
- **Attacker:** Sellsword with Longsword (Damage 2, Pierce 1)
- **Defender:** Guard with Round Shield (Block 3) and Chainmail Hauberk (Body DR 2)

**Attack Declaration:**
- Attacker targets Body location (+0 Difficulty)
- Uses Physique + Heavy Melee + Equipment Dice (always 2)
- Dice Pool: 5 + 2 + Equipment Dice (2) = 9 dice (includes the fixed 2 equipment dice)

**Defense Response:**
- Defender chooses to Block (3 AP cost)
- Uses Physique + Heavy Melee + Shield Block
- Dice Pool: 4 + 1 + 3 = 8 dice

**Resolution:**
- Attacker rolls 9 dice, gets 2 successes
- Defender rolls 8 dice, gets 1 success
- Attack succeeds with 1 extra success

**Damage Calculation:**
- Base Damage: 2
- Effective DR: 2 - 1 = 1
- Penetrating Damage: 2 - 1 = 1
- Result: Roll 1d6 on Body Wound Table

### Example 2: Crossbow Ambush

**Scenario:** Scout ambushes target with crossbow from concealment

**Attack Setup:**
- **Upper Hand:** +2 dice (surprise, good position)
- **Target:** Voids location (+3 Difficulty)
- **Dice Pool:** Precision + Ranged + Equipment Dice (always 2) + Upper Hand - Difficulty
- **Calculation:** 6 + 2 + Equipment Dice (2) + 2 - 3 = 9 dice (includes the fixed 2 equipment dice)

**Target Defense:**
- **Passive Defense Only:** AGI modifier applied as Difficulty
- **No Active Defense:** Target unaware of attack

**Resolution:**
- Attacker rolls 9 dice, gets 3 successes
- 2 extra successes available for Stunts
- Chooses: +1 damage, +1 knockback effect

**Damage Calculation:**
- Base Damage: 2 + 1 (Stunt) = 3
- Pierce: 2 (crossbow bolt)
- Target Armor: Gambeson (Voids DR 1)
- Effective DR: 1 - 2 = 0 (minimum 0)
- Penetrating Damage: 3 - 0 = 3
- Wound Roll: 3d6 on Voids Wound Table

---

