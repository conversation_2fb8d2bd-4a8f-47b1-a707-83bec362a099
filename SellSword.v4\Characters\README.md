# Character Creation & Advancement

*Domain-specific guide for SellSword v4 character creation and development*

*Comprehensive guide to creating sellswords and developing them through experience*

## Table of Contents

1. [Character Creation Philosophy](#character-creation-philosophy)
2. [Step-by-Step Creation](#step-by-step-creation)
3. [Character Concepts](#character-concepts)
4. [Starting Equipment](#starting-equipment)
5. [Advancement System](#advancement-system)
6. [Character Templates](#character-templates)
7. [Domain Context](#domain-context)
8. [Cross-References](#cross-references)

---

## Character Creation Philosophy

### The Sellsword Life

Characters in SellSword begin as competent professionals, not helpless novices. Every sellsword has:

- **Professional Competence:** Basic skills needed for mercenary work
- **Distinct Specialization:** Clear strengths that define their role
- **Meaningful Weaknesses:** Limitations that create interesting challenges
- **Growth Potential:** Clear paths for advancement through experience

### Design Principles

**Balanced Capability:** No character should be helpless in any situation, but each should excel in specific areas.

**Meaningful Choices:** Every decision during creation should have clear trade-offs and consequences.

**Equipment Dependency:** Characters rely on gear for capability, making equipment choices crucial.

**Advancement Through Experience:** Growth comes from facing challenges, not arbitrary progression.

---

## Step-by-Step Creation

### Step 1: Prime Attributes

Distribute **6 Prime Attributes** so the total equals **+3**:

- **Strength (STR)** - Raw physical power
- **Agility (AGI)** - Speed, reflexes, coordination  
- **Dexterity (DEX)** - Fine motor control, precision
- **Acuity (ACU)** - Awareness, perception, attention
- **Presence (PRE)** - Force of personality, leadership
- **Intellect (INT)** - Reasoning, memory, knowledge

**Constraints:**
- Total must equal +3
- No attribute may exceed +3 or -2
- Negative attributes allowed to balance higher positives

**Recommended Arrays:**
- **Balanced:** [+1, +1, +1, 0, 0, 0]
- **Specialist:** [+2, +1, 0, 0, 0, -1]
- **Focused:** [+3, +1, +1, +1, -1, -2]

### Step 2: Calculate Actions

**Actions** determine your base dice pools:

- **Physique** = 3 + STR + AGI *(forceful, dynamic actions)*
- **Finesse** = 3 + AGI + DEX *(graceful, quick actions)*
- **Precision** = 3 + DEX + ACU *(careful, accurate actions)*
- **Focus** = 3 + ACU + PRE *(sustained attention, willpower)*
- **Insight** = 3 + PRE + INT *(understanding, deduction)*

**Typical Ranges:** 2-9 dice (average 5-6 for most characters)

### Step 3: Derived Statistics

Calculate resources and capabilities:

- **Speed** = 4 + AGI + Mobility skill levels + Size Modifier - Encumbrance
- **Carry** = 4 + STR + Might skill levels + Size Modifier
- **Stamina** = Physique + Fortitude skill levels - Armor Fatigue
- **Will** = Focus + Resolve skill levels
- **Lore Slots** = 3 + (INT × 2) + Reason skill levels
- **Standing Slots** = 3 + PRE + Influence skill levels

*Note: Size Modifier is typically 0 for human-sized characters. See individual character options or monster rules for non-standard sizes.*

### Step 4: Skills

Spend **7 Skill Points (SP)** on skills:

**Skill Costs:**
- Level 1: 1 SP
- Level 2: 2 additional SP (3 total)
- Level 3: 3 additional SP (6 total)

**Skill Categories:**
- **Green Skills (Stamina Wager):** Fortitude, Heavy Melee, Might, Light Melee, Mobility
- **Yellow Skills (Time Wager):** Stealth, Subtlety, Ranged, Weave, Observe
- **Blue Skills (Will Wager):** Resolve, Evoke, Reason, Influence, Scry

**Recommended Distributions:**
- **Specialist:** 1 skill at Level 3, 1 skill at Level 1 (7 SP total)
- **Generalist:** 4 skills at Level 1, 1 skill at Level 2 (7 SP total)
- **Balanced:** 2 skills at Level 2, 1 skill at Level 1 (7 SP total)

### Step 5: Lore & Standing

**Lore Selection:**
- Choose Lore equal to your Lore Slots
- Required for magic: Must know relevant Wyrd Lore
- Examples: Fyr Lore, Military History, Regional Geography

**Standing Selection:**
- Choose Standing equal to your Standing Slots
- Represents social connections and reputation
- Examples: Guild Membership, Noble Favor, Criminal Contacts

### Step 6: Starting Equipment

Select gear based on character concept and available resources:

**Equipment Budget:** Varies by campaign (typically 50-100 silver)
**Essential Gear:** Weapon, armor, basic adventuring supplies
**Quality Considerations:** Better equipment provides significant advantages

---

## Character Concepts

### The Veteran Warrior

**Concept:** Experienced soldier turned mercenary
**Attributes:** STR +2, AGI +1, DEX 0, ACU +1, PRE 0, INT -1
**Key Skills:** Heavy Melee 2, Fortitude 1, Might 1, Observe 1, Influence 1
**Equipment Focus:** Quality armor and weapons
**Advancement Path:** Combat techniques, leadership skills

### The Skilled Scout

**Concept:** Mobile reconnaissance specialist
**Attributes:** STR 0, AGI +3, DEX +1, ACU +1, PRE 0, INT -2
**Key Skills:** Mobility 2, Stealth 2, Ranged 1
**Equipment Focus:** Light armor, ranged weapons, tools
**Advancement Path:** Exploration techniques, stealth mastery

### The Learned Mage

**Concept:** Scholar-warrior with magical training
**Attributes:** STR -1, AGI 0, DEX +1, ACU +1, PRE +1, INT +1
**Key Skills:** Reason 2, Evoke 1, Weave 1, Resolve 1, Scry 1
**Equipment Focus:** Magical implements, protective gear
**Advancement Path:** Additional Wyrds, magical techniques

### The Social Manipulator

**Concept:** Diplomat and information broker
**Attributes:** STR -1, AGI 0, DEX +1, ACU +1, PRE +3, INT -1
**Key Skills:** Influence 2, Observe 1, Subtlety 1, Reason 1, Influence 1
**Equipment Focus:** Quality clothing, tools, weapons
**Advancement Path:** Social techniques, information networks

---

## Starting Equipment

### Equipment Categories

**Weapons (Choose Primary & Secondary):**
- **Light Melee:** Dagger, Arming Sword, Rapier
- **Heavy Melee:** Longsword, War Hammer, Great Sword
- **Ranged:** Crossbow, Long Bow, Throwing Knives

**Armor (Choose Based on Concept):**
- **Light:** Gambeson, Leather Armor (minimal penalties)
- **Medium:** Studded Leather, Mail Shirt (moderate protection)
- **Heavy:** Full Mail, Plate Harness (maximum protection)

**Essential Gear:**
- **Light Source:** Torch, Lantern, Candles
- **Tools:** Rope, Crowbar, Lockpicks (as appropriate)
- **Supplies:** Rations, Water, Medical Kit
- **Clothing:** Travel clothes, cloak, boots

### Equipment Packages

**Warrior Package (75 silver):**
- Longsword, Dagger, Mail Shirt
- Round Shield, Crossbow with 20 bolts
- Torch, Rope, Rations (3 days)

**Scout Package (60 silver):**
- Arming Sword, Dagger, Leather Armor
- Long Bow with 30 arrows, Lockpicks
- Lantern with oil, Rope, Rations (5 days)

**Mage Package (80 silver):**
- Arming Sword, Dagger, Studded Leather
- Spell Components, Scholar's Kit
- Lantern with oil, Books, Rations (3 days)

---

## Advancement System

### Earning Experience

**Skill Points** earned through:
- **Mission Completion:** 1-3 SP based on difficulty and success
- **Significant Challenges:** 1 SP for overcoming major obstacles
- **Personal Goals:** 1 SP for achieving character-specific objectives
- **Exceptional Roleplay:** 1 SP for outstanding character moments

### Advancement Options

**Skill Improvement:**
- Increase existing skills (costs as per creation)
- Learn new core skills (1/3/6 SP for levels 1/2/3)
- Acquire techniques and masteries (variable costs)

**Capability Expansion:**
- New Lore (1 SP per Lore, requires teacher or research)
- New Standing (1 SP per Standing, requires in-game development)
- Equipment Mastery (through use and training)

### Advancement Guidelines

**Pacing:** 1-2 SP per session, 3-5 SP per major story arc
**Restrictions:** Some advances require in-game justification
**Balance:** No single character should dominate all areas

---

## Character Templates

### Quick Creation Templates

**Template Format:**
```
Name: [Character Type]
Attributes: [STR/AGI/DEX/ACU/PRE/INT]
Skills: [Skill list with levels]
Equipment: [Essential gear]
Concept: [Brief description]
```

**The Sellsword:**
- Attributes: [+1/+1/0/+1/0/0]
- Skills: Heavy Melee 2, Might 1, Fortitude 1, Observe 1
- Equipment: Longsword, Mail Shirt, Shield
- Concept: Reliable warrior for hire

**The Infiltrator:**
- Attributes: [0/+2/+1/+1/-1/0]
- Skills: Stealth 2, Subtlety 2, Light Melee 1
- Equipment: Dagger, Leather Armor, Lockpicks
- Concept: Specialist in covert operations

**The Hedge Wizard:**
- Attributes: [-1/0/+1/+1/0/+2]
- Skills: Evoke 1, Weave 1, Reason 2, Resolve 1
- Equipment: Staff, Robes, Spell Components
- Concept: Self-taught magical practitioner

---

## Domain Context

### Working in Characters Domain

**Before Making Changes:**
1. **Read the Style Guide:** [SellSword_v4_Style_Guide.md](../SellSword_v4_Style_Guide.md)
2. **Check Current Priorities:** [SellSword_v4_TODO.md](../SellSword_v4_TODO.md)
3. **Review Integration Points:** How changes affect Skills, Equipment, and Combat systems
4. **Consider Balance:** Character power level vs. advancement pacing

### Key Formatting Standards

**Character Stat Blocks:**
```markdown
**[Character Type]:**
- **Attributes:** [STR/AGI/DEX/ACU/PRE/INT array]
- **Key Skills:** [Skill list with levels]
- **Equipment Focus:** [Primary gear categories]
- **Advancement Path:** [Suggested development]
```

**Creation Steps Format:**
```markdown
### Step #: [Step Name]

[Clear description of what this step accomplishes]

**[Subsection]:**
- [Specific instruction or option]
- [Clear mechanical effect]

**Example:** [Concrete example showing the step]
```

### Terminology Standards

**Character Terms:**
- **Prime Attributes:** The six core attributes (STR, AGI, DEX, ACU, PRE, INT)
- **Actions:** Derived statistics for dice pools (Physique, Finesse, Precision, Focus, Insight)
- **Skill Points (SP):** Currency for character advancement
- **Lore/Standing Slots:** Social and knowledge connections

**Resource Terms:**
- **Stamina:** Physical energy pool
- **Will:** Mental energy pool
- **Speed:** Movement capability
- **Carry:** Equipment capacity

### Integration Points

**Skills System:**
- **Starting Skills:** 7 SP distributed among available skills
- **Skill Categories:** Organized by associated Actions
- **Prerequisites:** Advanced skills require foundational skills
- **Advancement:** Skills improve through experience and training

**Equipment System:**
- **Starting Budget:** Varies by campaign (50-100 silver typical)
- **Carry Capacity:** STR and Might determine equipment limits
- **Quality Impact:** Better equipment provides significant advantages
- **Maintenance:** Equipment requires care and resources

**Combat System:**
- **Action Dice Pools:** Attributes + Skills + Equipment modifiers
- **Resource Management:** Stamina and Will expenditure in combat
- **Wound Effects:** Injuries reduce capability over time
- **Equipment Dependency:** Gear quality affects combat effectiveness

### Balance Considerations

**Attribute Distribution:** Total +3 prevents min-maxing while allowing specialization
**Skill Point Economy:** 7 SP forces meaningful choices without crippling characters
**Equipment Budget:** Starting gear should be functional but not optimal
**Advancement Pacing:** 1-2 SP per session maintains steady growth

### Content Templates

**New Character Concept Template:**
```markdown
### The [Character Type]

**Concept:** [Brief thematic description]
**Attributes:** [STR/AGI/DEX/ACU/PRE/INT array totaling +3]
**Key Skills:** [Skill list with levels, totaling 7 SP]
**Equipment Focus:** [Primary gear categories and priorities]
**Advancement Path:** [Suggested techniques and masteries]
**Tactical Role:** [How this character contributes to group]
**Weaknesses:** [Areas where character struggles]
```

**Character Sheet Template:**
```markdown
## [Character Name]

**Concept:** [Brief description]

### Attributes
- STR: [+/-#] | AGI: [+/-#] | DEX: [+/-#]
- ACU: [+/-#] | PRE: [+/-#] | INT: [+/-#]

### Actions
- Physique: [#] | Finesse: [#] | Precision: [#]
- Focus: [#] | Insight: [#]

### Resources
- Speed: [#] | Carry: [#] | Stamina: [#] | Will: [#]

### Skills
- [Skill Name] [Level]: [Description]

### Equipment
- [Item]: [Stats and notes]

### Lore & Standing
- **Lore:** [List of known lore]
- **Standing:** [List of social connections]
```

---

## Cross-References

### Related Systems
- **Core Mechanics:** See [SellSword_v4_Core_Rules.md](../SellSword_v4_Core_Rules.md) for dice pool system
- **Skills:** See [Skills README](../Skills/README.md) for detailed skill descriptions
- **Equipment:** See [Equipment README](../Equipment/README.md) for gear statistics and costs
- **Magic:** See [Magic README](../Magic/README.md) for Lore requirements and magical abilities

### Character Integration
- **Combat Capability:** Determined by attributes, skills, and equipment quality
- **Social Influence:** Based on Presence, Influence skill, and Standing relationships
- **Magical Potential:** Requires appropriate Lore and magic skills (Evoke, Weave, Scry)
- **Advancement Paths:** Guided by character concept and player goals

### Character Sheet Resources
- **Basic Character Sheet:** [sellSword_charForm.pdf](sellSword_charForm.pdf)
- **Jarriki Variant:** [sellSword_charForm_jarriki.pdf](sellSword_charForm_jarriki.pdf)
- **Additional Forms:** See other PDF files in this directory

---

*Character creation establishes the foundation for a sellsword's career. Advancement through experience shapes their development into legendary figures.*

**Version:** 4.0
**Last Updated:** December 15, 2024
