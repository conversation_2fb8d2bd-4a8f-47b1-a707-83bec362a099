# Wounds System

*Comprehensive guide to injury, recovery, and the consequences of violence*

## Table of Contents

1. [Wounds Philosophy](#wounds-philosophy)
2. [Wound Mechanics](#wound-mechanics)
3. [Wound Tables](#wound-tables)
4. [Wound Effects](#wound-effects)
5. [Recovery & Healing](#recovery--healing)
6. [Special Wound Types](#special-wound-types)
7. [Wound Examples](#wound-examples)
8. [Cross-References](#cross-references)

---

## Wounds Philosophy

### No Hit Points

SellSword uses a **wound-based system** instead of hit points. Each injury has specific mechanical effects that reflect the nature and severity of the wound.

### Design Principles

**Realistic Consequences:** Wounds affect capability in logical ways
**Tactical Significance:** Different wound locations create different challenges
**Recovery Takes Time:** Healing is a process, not instant restoration
**Equipment Matters:** Armor location determines wound severity

### Wound Severity Scale

**Minor Wounds:** Temporary penalties, quick recovery
**Moderate Wounds:** Significant impairment, requires treatment
**Severe Wounds:** Major disability, long-term consequences
**Critical Wounds:** Life-threatening, immediate medical attention required

---

## Wound Mechanics

### Wound Generation

**Trigger:** Penetrating damage from successful attacks
**Dice Rolled:** Equal to penetrating damage amount
**Table Used:** Determined by hit location (Body, Voids, Vitals)
**Result:** Specific wound with mechanical effects

### Wound Accumulation

**Multiple Wounds:** Characters can suffer multiple wounds simultaneously
**Cumulative Effects:** Wound penalties stack unless otherwise noted
**Location Tracking:** Track wounds by body location
**Severity Progression:** Multiple wounds to same location may worsen effects

### Wound Resistance

**Armor Protection:** Higher DR reduces penetrating damage
**Fortitude Skill:** May provide resistance to wound effects
**Magical Protection:** Some spells or items reduce wound severity
**Environmental Factors:** Cold, heat, or other conditions may worsen wounds

---

## Wound Tables

### Body Wounds (Torso & Limbs)

*Roll 1d6 per point of penetrating damage, use highest result*

| Roll | Wound Type | Effect | Recovery |
|------|------------|--------|----------|
| 1-2 | Bruise | -1 die to actions using wounded location | 1 day |
| 3-4 | Cut | -2 dice to actions using wounded location | 3 days |
| 5 | Deep Wound | -3 dice, bleeding (1 Stamina/hour) | 1 week |
| 6 | Severe Injury | -4 dice, bleeding (1 Stamina/10 min), shock | 2 weeks |

### Voids Wounds (Gaps & Joints)

*Roll 1d6 per point of penetrating damage, use highest result*

| Roll | Wound Type | Effect | Recovery |
|------|------------|--------|----------|
| 1-2 | Scrape | -1 die to Finesse and Precision actions | 2 days |
| 3-4 | Joint Strain | -2 dice to movement and manipulation | 5 days |
| 5 | Torn Muscle | -3 dice to affected actions, -1 Speed | 10 days |
| 6 | Severed Tendon | -4 dice, limb partially disabled | 3 weeks |

### Vitals Wounds (Head & Heart)

*Roll 1d6 per point of penetrating damage, use highest result*

| Roll | Wound Type | Effect | Recovery |
|------|------------|--------|----------|
| 1-2 | Glancing Blow | -1 die to Focus and Insight actions | 3 days |
| 3-4 | Concussion | -2 dice to mental actions, nausea | 1 week |
| 5 | Serious Trauma | -3 dice to all actions, unconscious 1d6 rounds | 2 weeks |
| 6 | Critical Injury | Incapacitated, dying (1 Stamina/minute) | 1 month |

---

## Wound Effects

### Action Penalties

**Dice Reduction:** Wounds reduce dice pools for relevant actions
**Stacking Penalties:** Multiple wounds to same location stack
**Location Specificity:** Arm wounds affect manipulation, leg wounds affect movement
**Mental Wounds:** Head injuries affect cognitive abilities

### Special Conditions

#### Bleeding
- **Effect:** Lose specified Stamina at regular intervals
- **Treatment:** First aid stops bleeding temporarily
- **Healing:** Stops naturally when wound begins to heal

#### Shock
- **Effect:** -2 dice to all actions for 1 hour
- **Cause:** Severe trauma or multiple wounds
- **Treatment:** Medical attention or magical healing

#### Unconsciousness
- **Effect:** Character cannot act
- **Duration:** Specified time or until awakened
- **Recovery:** Natural awakening or medical intervention

#### Incapacitation
- **Effect:** Character cannot take meaningful actions
- **Cause:** Overwhelming pain or trauma
- **Treatment:** Requires immediate medical attention

### Death and Dying

**Dying Condition:** Triggered by Critical Vitals wounds or Stamina reaching 0
**Death Saves:** Focus + Resolve roll each minute to stabilize
**Stabilization:** Successful medical treatment stops dying process
**Permanent Death:** Failure to stabilize within Constitution minutes

---

## Recovery & Healing

### Natural Healing

**Rest Requirements:** Full rest accelerates healing
**Activity Penalties:** Strenuous activity slows recovery
**Nutrition:** Proper food and water aid healing
**Environmental Factors:** Clean, warm conditions improve recovery

### Medical Treatment

#### First Aid (1 AP)
- **Skill:** Reason + appropriate tools
- **Effect:** Stop bleeding, stabilize dying characters
- **Limitation:** Temporary relief, not true healing

#### Physik Treatment (Exploration AP)
- **Skill:** Reason + medical supplies
- **Effect:** Reduce recovery time by 25-50%
- **Requirements:** Proper tools and clean environment

#### Surgical Intervention (Downtime)
- **Skill:** Reason + surgical tools
- **Effect:** Treat severe wounds, prevent complications
- **Risk:** Failure may worsen condition

### Magical Healing

#### Lif Wyrd Healing
- **Effect:** Accelerate natural healing processes
- **Limitation:** Cannot heal beyond natural capability
- **Cost:** Will expenditure based on wound severity

#### Divine Intervention
- **Effect:** Miraculous healing of any wound
- **Limitation:** Extremely rare, requires divine favor
- **Cost:** Significant spiritual obligation

---

## Special Wound Types

### Aetheric Wounds

**Cause:** Magical mishaps or exposure to raw Aetheric energy
**Effect:** Disrupts magical abilities and Wyrd connections
**Treatment:** Requires magical expertise, not mundane physik
**Recovery:** Slower than physical wounds, may leave permanent effects

### Poison Wounds

**Cause:** Envenomed weapons or toxic substances
**Effect:** Additional ongoing damage or debilitating effects
**Treatment:** Antidotes or magical purification
**Progression:** May worsen over time without treatment

### Cursed Wounds

**Cause:** Magical weapons or supernatural attacks
**Effect:** Resist normal healing, may have supernatural effects
**Treatment:** Requires magical intervention or curse removal
**Persistence:** May never heal without breaking the curse

### Environmental Wounds

**Frostbite:** Cold exposure, reduces manual dexterity
**Burns:** Fire damage, painful and slow to heal
**Disease:** Infection, spreads and worsens over time
**Exhaustion:** Extreme fatigue, affects all actions

---

## Wound Examples

### Example 1: Sword Cut to Arm

**Scenario:** Longsword (Damage 2, Pierce 1) hits leather armor (Body DR 2)
- **Effective DR:** 2 - 1 = 1
- **Penetrating Damage:** 2 - 1 = 1
- **Wound Roll:** 1d6 = 4 (Cut)
- **Effect:** -2 dice to actions using that arm
- **Recovery:** 3 days with rest

### Example 2: Crossbow Bolt to Joint

**Scenario:** Crossbow (Damage 2, Pierce 2) hits mail armor gaps (Voids DR 2)
- **Effective DR:** 2 - 2 = 0
- **Penetrating Damage:** 2 - 0 = 2
- **Wound Roll:** 2d6 = 3, 5 (use highest: 5)
- **Result:** Torn Muscle
- **Effect:** -3 dice to affected actions, -1 Speed
- **Recovery:** 10 days with proper treatment

### Example 3: War Hammer to Head

**Scenario:** War Hammer (Damage 2, Pierce 2) hits unarmored head (Vitals DR 0)
- **Effective DR:** 0 - 2 = 0 (minimum 0)
- **Penetrating Damage:** 2 - 0 = 2
- **Wound Roll:** 2d6 = 4, 6 (use highest: 6)
- **Result:** Critical Injury
- **Effect:** Incapacitated, dying (1 Stamina/minute)
- **Treatment:** Immediate medical attention required

---

## Cross-References

### Related Systems
- **Combat:** See [README.md](vsProjects/AIGM2/ttrpg-gm-ai/README.md) for damage resolution
- **Equipment:** See [Equipment README](../Equipment/README.md) for armor protection values
- **Skills:** See [Skills README](../Skills/README.md) for medical and recovery skills
- **Magic:** See [Magic README](../Magic/README.md) for magical healing options

### Wound Integration
- **Character Capability:** Wounds directly impact action dice pools and capabilities
- **Equipment Importance:** Armor location and quality determine wound severity
- **Resource Management:** Healing requires time, supplies, and sometimes magical intervention
- **Tactical Considerations:** Wound accumulation affects combat effectiveness over time

---

*The wound system creates lasting consequences for violence while providing multiple paths to recovery and healing.*

**Version:** 4.0  
**Last Updated:** December 15, 2024
