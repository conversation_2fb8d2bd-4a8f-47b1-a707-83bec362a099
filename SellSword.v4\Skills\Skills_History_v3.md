# SellSword v3 Skills System

*Historical documentation of the refined Category/Style/Specialization system*

**Purpose:** Document v3 skills system for historical reference and analysis

---

## System Overview

### Design Philosophy
- **Refined Three-Tier System** - Category/Style/Specialization with improved balance
- **Level-less Upper Tiers** - Styles and Specializations are single purchases
- **Mechanical Innovation Focus** - Higher tiers grant distinct advantages, not dice stacking
- **Playstyle Differentiation** - Styles create dramatically different approaches

### Core Mechanics
- **Tier 1 (Category):** Broad competence, 3 levels, escalating costs (2/3/4 SP)
- **Tier 2 (Style):** Specific approach, level-less, flat 3 SP cost
- **Tier 3 (Specialization):** Niche mastery, level-less, flat 3 SP cost
- **Prerequisites:** Styles require Category L1, Specializations require Category L2

### Key Refinements from v2
- **Standardized Costs** - All Styles and Specializations cost 3 SP
- **Simplified Prerequisites** - Clearer advancement requirements
- **Enhanced Discipline** - Added mental training Category skill
- **Improved Balance** - Power levels more consistent across options

---

## Complete v3 Category Skills List

### I. Physical & Combat Skills

#### **1. Athletics (FRT/PHY/FIN)**
*Adds dice to climbing, jumping, swimming, balancing, tumbling, endurance*
*Secondary: +1 Stamina per level*

**Styles:**
- **Marathoner** (FRT) - Ignore major difficulty for endurance, +1 Stamina when 'Taking a Breather'
- **Powerhouse** (PHY) - Ignore major difficulty for strength feats, +2 to Carry
- **Acrobat** (FIN) - Ignore major difficulty for acrobatic feats, +1 Speed

**Specializations:**
- **Pack Mule** - Ignore 1 level of Fatigue from heavy loads
- **Wanderer** - Ignore 1 level of Fatigue from environmental effects, +1 Will in adverse conditions
- **Juggernaut** - Spend 1 Stamina to reduce melee attack AP costs by 1
- **Sprinter** - Spend 1 Stamina to increase movement by PHY score
- **Combat Reflexes** - Spend Stamina to reduce dodging/tumbling AP costs
- **Parkour** - Spend Stamina on FIN actions for enhanced movement

#### **2. Discipline (FCS/RES/INS)**
*Adds dice to reasoning, focus, willpower, and mental resilience*
*Secondary: +1 Will per level*

**Styles:**
- **Flame and the Void** (FCS) - Ignore major difficulty for concentration, +1 Stamina when 'Taking a Breather'
- **Iron Will** (RES) - Ignore major difficulty for mental pressure, +2 to Carry
- **Insight Style** (INS) - Ignore major difficulty for deduction, +1 Speed

**Specializations:**
- **Focus Specialization (martial)** - Ignore 1 level of Fatigue, reduce movement AP costs when encumbered
- **Focus Specialization (mental)** - Reduce environmental challenge Difficulty by 2, +1 Will in adverse conditions
- **Body focused** - Spend Will in place of Stamina for physical actions
- **Mind focused** - Will dice succeed on 5's when resisting mental pressure
- **People focused** - Spend Stamina to reduce dodging/tumbling AP costs
- **Environment focused** - Ignore minor difficulty for terrain movement/climbing

#### **3. Weapons Craft (PHY/FIN/PRC)**
*Adds dice to melee attacks, parries, blocks using weapons*
*Secondary: +1 to weapon durability per level*

**Styles:**
- **Defender** (FRT) - Ignore minor difficulty for blocking/parrying, +1 to armor durability
- **Two-Handed** (PHY) - Enhanced force and area denial with two-handed weapons
- **Dueling** (FIN) - Access to Parry/Riposte Stunts, improved single opponent defense
- **Ambidexterity** (DEX) - Reduce penalties for fighting with two weapons

**Specializations:**
- **Ironclad** - Apply +Damage stunt up to armor value when brawling, ignore 1 Fatigue/Encumbrance from armor
- **Shield Master** - Use "Gain Momentum" and "Put Off Balance" stunts when blocking, spend Stamina to reduce block AP costs
- **Great Weapons** - Increase Apply Force or Cleave Stunt effects by +1
- **Polearms** - Enhance reach/trip Stunts, reduce Difficulty when bracing against charges
- **Staves** - Enhance defensive/trip/disarm Stunts with staves
- **Swords (One-Handed)** - Reduce Parry/Riposte Stunt costs by 1 success
- **Daggers** - Reduce Complexity by 1 for Voids/Vitals attacks with daggers
- **Two-Weapon Fighting** - Combine equipment stats of both weapons for single actions
- **Paired Light Weapons** - Enhanced Stunts when using paired light weapons
- **Sword and Dagger** - Enhanced offense/defense combination Stunts

#### **4. Brawling (PHY/FIN)**
*Adds dice to unarmed attacks, grapples, shoves, trips*

**Styles:**
- **Grappler** (PHY) - Improved Stunts for holding/pinning opponents, apply locks
- **Striker** (FIN) - Faster unarmed strikes, Stunts for disorienting opponents

**Specializations:**
- **Bone Breaker** - Inflict minor wounds or conditions via grappling
- **Throw** - Apply Force Stunt can throw grappled target 1 hex per Force effect
- **Dirty Fighting** - Reduce Complexity/Difficulty for eye pokes, groin kicks, etc.
- **Iron Fists** - Unarmed strikes deal more base damage or have minor armor penetration

#### **5. Ranged Combat (PRC)**
*Adds dice to ranged attacks with bows, crossbows, slings, thrown weapons*
*Secondary: Adds to base range multiplier*

**Styles:**
- **Marksman** - Reduce Complexity by 1 for aimed shots, Target Weakness Stunt ignores 2 Armor Value
- **Skirmisher** - Reduce Complexity for attacks while moving, reduce AP cost for drawing/stowing weapons

**Specializations:**
- **Bows** - Reduce Difficulty by 2 when making attacks with any bow
- **Crossbows** - Reduce Difficulty by 1 for crossbow attacks, reduce loading AP cost by 1
- **Slings** - Reduce Difficulty by 2 for sling attacks, Apply Force Stunt gains +1 Force effect
- **Thrown Weapons** - Reduce Difficulty by 1 for thrown weapon attacks, +2 hexes range

### II. Exploration & General Skills

#### **6. Husbandry (FIN/INS)**
*Adds dice to riding, training, or calming animals*

**Styles:**
- **Rider** (FIN) - Improved control in combat, reduce Difficulty for difficult maneuvers
- **Animal Ken** (INS) - Better gauge animal moods, reduce Difficulty for calming/training

**Specializations:**
- **Cavalry Tactics** - Enhanced mounted combat formations and charges
- **Trick Riding** - Acrobatic feats on horseback (vaulting, shooting)
- **Beast Tamer** - Training animals for specific tasks (guarding, hunting)
- **Veterinarian** - Reduce Difficulty by 1 for healing animal wounds

#### **7. Subterfuge (FIN/PRC)**
*Adds dice to stealth, lockpicking, traps, disguise, sleight of hand*

**Styles:**
- **Infiltrator** (FIN) - Reduce Complexity/Difficulty for moving silently, climbing unseen
- **Saboteur** (PRC) - Reduce Difficulty/Complexity for setting/disarming traps, sabotage
- **Deceiver** (INS) - Reduce Difficulty for creating disguises, forging documents, sleight of hand

**Specializations:**
- **Cat Burglar** - Reduce Difficulty by 1 for bypassing specific obstacles (windows, rooftops)
- **Vanish** - Enables the 'Vanish' Stunt (quick disappearance after distraction)
- **Lockpicking** - Significantly reduce Difficulty for picking locks
- **Trapfinding** - Reduce Difficulty by 1 for spotting/analyzing traps
- **Impersonation** - Allows attempting to mimic voices and mannerisms
- **Pickpocket** - Reduce Difficulty/Complexity for lifting items

#### **8. Survival (FRT/FCS)**
*Adds dice to tracking, foraging, navigating wilderness, resisting exposure*

**Styles:**
- **Tracker** (FCS) - Reduce Difficulty for finding/following tracks, identify creature type/number
- **Forager** (AWA) - Reduce Difficulty for finding food/water, identifying useful plants
- **Pioneer** (FRT) - Reduce Difficulty for navigating difficult terrain, building shelters, resisting elements

**Specializations:**
- **Urban Tracking** - Apply tracking skills in settlements
- **Monster Hunter** - Apply Survival/Lore skills to identify monster signs and predict behavior
- **Herbalist** - Apply Survival/Physik skills to identify medicinal or poisonous plants
- **Trapper** - Craft and set simple snares/traps for food
- **Cartographer** - Allows creating accurate maps

### III. Knowledge & Social Skills

#### **9. Crafting (PRC/INT)**
*Adds dice to creating or repairing items (weapons, armor, tools)*

**Styles:**
- **Smith** (STR/PRC) - Focus on metalworking (armor, weapons)
- **Artisan** (DEX/INT) - Focus on finer crafts (leatherworking, fletching, jewelry)
- **Alchemist** (INT) - Brew potions, poultices, acids, explosives

**Specializations:**
- **Weaponsmith** - Craft/repair higher quality weapons, apply Crafting to understand weapon flaws
- **Armorsmith** - Craft/repair higher quality armor, apply Crafting to fit armor
- **Fletcher** - Craft superior arrows/bolts
- **Leatherworker** - Craft/repair leather armor, pouches, etc.
- **Poisons** - Craft various toxins, apply Crafting/Physik to identify toxins
- **Elixirs** - Create beneficial (non-magical) concoctions

#### **10. Influence (RES/INS)**
*Adds dice to persuasion, intimidation, deception, leadership, etiquette*

**Styles:**
- **Orator** (PRE) - Inspire crowds, improved Stunts for persuasion/inspiration
- **Interrogator** (RES) - Break down resistance, improved Stunts for intimidation/coercion
- **Manipulator** (INS) - Improved Stunts for deception, reading motives

**Specializations:**
- **Diplomacy** - Apply Influence effectively in formal social situations, understand etiquette
- **Incite** - Apply Influence to stir crowds to action (or panic)
- **Intimidate** - Reduce Difficulty by 1 for overt threats
- **Subtle Threat** - Apply Influence to imply consequences without direct threats
- **Fast Talk** - Apply Influence for quick lies and bluffing
- **Seduction** - Apply Influence using charm or allure

#### **11. Physik (PRC/INT)**
*Adds dice to diagnosing ailments, treating wounds, knowledge of anatomy*

**Styles:**
- **Surgeon** (PRC) - Reduce Complexity/Difficulty for treating serious wounds, surgery
- **Diagnostician** (INT) - Reduce Difficulty for identifying diseases, poisons, causes of death

**Specializations:**
- **Field Dressing** - Faster/more effective stabilization of wounds in combat
- **Anatomy** - Apply Physik skill to identify weak points (synergy with Target Weakness Stunt)
- **Toxicology** - Apply Physik skill to identify specific poisons and their effects/antidotes
- **Pathology** - Apply Physik skill to understand diseases and their progression

#### **12. Tactics (FCS/INS)**
*Adds dice to assessing battlefields, coordinating allies, predicting enemy actions*

**Styles:**
- **Commander** (PRE) - Grant bonus dice or Stunts to allies via commands
- **Skirmish Leader** (FCS) - Coordinate small unit movements, ambushes, flanking
- **Strategist** (INT) - Analyze enemy capabilities, predict long-term actions, plan campaigns

**Specializations:**
- **Formation Fighting** - Apply Tactics skill to coordinate specific unit maneuvers
- **Battlefield Logistics** - Apply Tactics skill to assess supply lines and fortifications
- **Ambush** - Reduce Difficulty by 1 for setting up ambushes
- **Counter-Tactic** - Apply Tactics skill to identify and counter specific enemy maneuvers
- **Siegecraft** - Apply Tactics skill to understand assaulting/defending fortifications

### IV. Magic Skills (Wyrd Manipulation)

#### **13. Evoke (RES)**
*Adds dice to gathering raw Aether associated with a known Wyrd*

**Styles:**
- **Channeler** (CON) - Gather Aether more quickly or hold more before shaping
- **Raw Power** (RES) - Overcharge Evocation for greater magnitude at higher risk (Mishap)

**Specializations:**
- **Wyrd Resonance [Wyrd Name]** - Reduce Difficulty by 1 for Evoking a specific Wyrd
- **Destructive Surge** - Enhance damaging effects of Evoked power

#### **14. Weave (FCS)**
*Adds dice to shaping, controlling, and maintaining gathered Aether*

**Styles:**
- **Shaper** (FCS) - Reduce Complexity for intricate or multi-target effects
- **Subtle Hand** (INS) - Reduce Difficulty for creating illusions or subtle manipulations

**Specializations:**
- **Wyrd Control [Wyrd Name]** - Reduce Difficulty by 1 for Weaving a specific Wyrd
- **Sustained Effects** - Reduce Strain/Difficulty for maintaining long-duration effects
- **Illusion Craft** - Create more convincing or complex illusions

#### **15. Scribe (PRC)**
*Adds dice to binding Aether into objects, runes, or locations*

**Styles:**
- **Runesmith** (PRC) - Reduce Complexity/Difficulty for creating potent or complex runes
- **Artificer** (INT) - Imbue items with temporary or triggered magical effects

**Specializations:**
- **Wyrd Binding [Wyrd Name]** - Reduce Difficulty by 1 for Scribing a specific Wyrd
- **Wards** - Create more durable or potent protective sigils
- **Enchant Weapon/Armor** - Apply Scribe skill to temporarily enhance gear
- **Potion Brewing (Magical)** - Bind magical effects into consumable liquids

#### **16. Scry (INS)**
*Adds dice to perceiving information or energies via the Aether*

**Styles:**
- **Seer** (AWA) - Reduce Difficulty for divination, foresight, reading auras
- **Empath** (PRE) - Reduce Difficulty for sensing emotions, communicating telepathically

**Specializations:**
- **Wyrd Sense [Wyrd Name]** - Reduce Difficulty by 1 for Scrying related to a specific Wyrd
- **Truth Reading** - Enhanced ability to detect lies or illusions via Scrying
- **Mind Reading** - Apply Scry skill to attempt glimpsing surface thoughts (high Difficulty/Complexity)

---

## System Strengths

### Refined Balance
- **Consistent Costs** - All Styles and Specializations cost 3 SP
- **Clear Prerequisites** - Simplified advancement requirements
- **Power Level Consistency** - More balanced options across all tiers

### Complete Coverage
- **All Archetypes** - Every character concept supported with clear paths
- **Mental Training** - Discipline skill added for focused warrior concepts
- **Comprehensive Magic** - Full magical character support

---

## System Weaknesses

### Complexity Concerns
- **Many Options** - 16 Category skills with extensive Style/Specialization trees
- **Decision Paralysis** - Too many choices could overwhelm new players
- **Documentation Heavy** - Required extensive documentation for all options

### Transition Issues
- **v4 Compatibility** - Some concepts didn't translate well to v4's streamlined approach
- **Redundancy** - Some overlap between similar skills and specializations

---

## Legacy for v4

### Successful Elements
- **Mechanical Innovation** - Focus on unique abilities over dice bonuses
- **Playstyle Differentiation** - Styles creating distinct approaches
- **Level-less Upper Tiers** - Avoiding incrementalism in specializations

### Areas for Improvement
- **Streamlining** - Need to reduce complexity while maintaining coverage
- **Integration** - Better integration with v4's action type system
- **Focus** - Clearer distinction between essential and optional content

*This v3 system represented the peak of the Category/Style/Specialization approach before v4's streamlined redesign.*
