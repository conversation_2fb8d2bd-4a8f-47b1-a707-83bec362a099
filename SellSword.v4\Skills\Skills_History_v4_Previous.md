# SellSword v4 Skills System (Previous Version)

*Historical documentation of the v4 skills system with extensive techniques and masteries*

*This version contained the full technique/mastery framework - archived for reference*

## Overview

The SellSword v4 skills system uses a three-tier structure designed to create dramatically different playstyles through mechanical innovation rather than simple dice bonuses.

### Design Philosophy

- **Core Skills** provide foundational competence and dice bonuses
- **Techniques** dramatically alter how you approach actions, creating unique playstyles
- **Masteries** represent deep expertise with specific tools, equipment, or applications
- **Mechanical Innovation** over simple number increases
- **Resource Flexibility** - skills enable novel uses of Stamina and Will
- **Equipment Synergy** - advanced skills interact meaningfully with gear

---

## Tier 1: Core Skills (Foundation)

*Cost: Level 1 = 1 SP, Level 2 = 2 additional SP, Level 3 = 3 additional SP*

These 15 skills form the foundation of character capability, adding dice to actions and providing secondary benefits.

### Physique Skills (Green - Stamina Wager)
- **Fortitude** - Endurance, resisting physical effects *(+1 Stamina per level)*
- **Heavy Melee** - Heavy weapons and shields, forceful combat *(+1 Force per level)*
- **Might** - Raw physical power, lifting, endurance *(+1 Carry per level)*

### Finesse Skills (Green - Stamina Wager)
- **Mobility** - Movement, dodging, acrobatics *(+1 Speed per level)*
- **Light Melee** - Light weapons and shields, finesse combat

### Precision Skills (Yellow - Time Wager)
- **Stealth** - Moving unseen, hiding
- **Subtlety** - Lockpicking, traps, sleight of hand
- **Ranged** - Bows, crossbows, thrown weapons *(+1 Range per level)*
- **Weave** - Shaping magical effects *(requires Wyrd Lore)*
- **Observe** - Searching, tracking, noticing details

### Focus Skills (Blue - Will Wager)
- **Resolve** - Mental fortitude, resisting mental effects *(+1 Will per level)*
- **Evoke** - Gathering magical power *(requires Wyrd Lore)*

### Insight Skills (Blue - Will Wager)
- **Reason** - Logic, investigation, problem-solving *(+1 Lore Slot per level)*
- **Influence** - Persuasion, intimidation, leadership *(+1 Standing per level)*
- **Scry** - Magical perception, divination *(requires Wyrd Lore)*

---

## Tier 2: Techniques (Playstyle Differentiators)

*Cost: Level 1 = 2 SP, Level 2 = 3 additional SP*

Techniques fundamentally change how you approach actions, creating distinct playstyles through unique mechanics. Focus on allowing players to do things better and faster than standard approaches.

### Combat Techniques

#### **Defender** (Heavy Melee 1)
*Prerequisite: Heavy Melee Level 1*

You excel at protecting yourself and others through defensive combat.

- **Resource:** May spend 1 Stamina to lower AP cost by 1 when blocking
- **Stunt Enhancement:** May use "Off-Balance" Stunt when blocking
- **Shield Slam:** When you successfully block, spend 1 additional Stamina to knockback the attacker

#### **Dual Wielding** (Physique/Finesse)
*Prerequisite: Heavy or Light Melee Level 1*

You fight with a weapon in each hand, creating a whirlwind of steel.

- **Resource:** When fighting with two weapons you may spend either 1 Stamina or 1 AP to combine both weapons' attributes into a single action
- **Stamina Efficiency:** May spend Stamina to use both weapons' special properties in a single attack
- **Defensive Flow:** When parrying with dual weapons, add the off-hand weapon's Parry value as bonus dice

#### **Weapon Master** (Physique/Finesse)
*Prerequisite: Any Melee Skill Level 2*

You have achieved mastery over the fundamental principles of armed combat.

- **Adaptive Combat:** May use any melee weapon without penalty, regardless of training
- **Equipment Mastery:** Add +1 to any one weapon stat (Offense, Damage, Pierce, etc.) when using masterwork weapons
- **Combat Reflexes:** Spend 1 Stamina to reduce the AP cost of weapon actions by 1 (minimum 1)

#### **Skirmisher** (Finesse)
*Prerequisite: Light Melee Level 1 OR Ranged Level 1*

You excel at mobile, hit-and-run tactics.

- **Mobile Combat:** No penalty for attacking while moving
- **Quick Draw:** Drawing/stowing weapons costs 0 AP
- **Evasive Dodge:** Spend 1 Stamina instead of 1 AP to dodge an attack

#### **Duelist** (Finesse)
*Prerequisite: Light Melee Level 1*

You specialize in precise one-on-one combat with finesse weapons.

- **Efficient Parry:** Spend 1 Stamina instead of weapon's Carry AP to parry an attack
- **Riposte:** When you successfully parry, spend 1 additional Stamina to immediately counterattack
- **Defensive Stance:** Gain +1 die to all parry attempts when fighting a single opponent

#### **Battle Frenzy** (Resolve)
*Prerequisite: Resolve Level 1*

You enter a focused state that allows you to push through pain and exhaustion through sheer willpower.

**Level 1:** (Cost: 2 SP)
- **Resource:** Spend 1 Will to enter a battle frenzy lasting until end of scene
- **Push Through:** Spend 2 Will to continue acting normally when you would be incapacitated
- **Mental Fortitude:** While in frenzy, ignore minor wound penalties and treat incapacitation as dazed

**Level 2:** *(Additional 3 SP)*
- **Unstoppable:** While in frenzy, gain +1 will die to all actions for each minor wound ignored.
- **Red Rage:** While in frenzy, whenever you roll 2 1's on any will dice you enter a berserk state, attacking nearest enemy or ally until frenzy ends 

#### **The Flame and the Void** (Resolve)
*Prerequisite: Resolve Level 2*

You have trained to achieve perfect coordination of mind and body through intense focus.

**Level 1:** (Cost: 2 SP)
- **Resource:** May wager Will when performing Finesse or Precision actions
- **Mental Recovery:** May spend 1 exploration AP to recover 1 Will
- **Mental Discipline:** Ignore Minor (-2) Difficulty from distractions or pressure

**Level 2:** *(Additional 3 SP)*
- **Action Enhancement:** Spend 1 Will + 1 AP to add your Focus to any action
- **Mind Over Matter:** May wager Will interchangeably with Stamina for any action

### Ranged Techniques

#### **Rapid Fire** (Finesse)
*Prerequisite: Ranged Level 1*

You sacrifice accuracy for volume of fire.

- **Multiple Shots:** May make additional ranged attacks at cumulative -1 die penalty
- **Suppression:** Spend Stamina to force enemies to make Focus checks or lose actions
- **Reload Mastery:** Reduce reload times by 1 AP (minimum 1)

### Exploration Techniques

#### **Urban Shadow** (Finesse)
*Prerequisite: Stealth Level 1*

You are a master of moving unseen in civilized areas.

- **Crowd Blend:** May use crowds as cover, gaining stealth bonuses in populated areas
- **Rooftop Runner:** No penalties for moving across rooftops or urban obstacles
- **Social Camouflage:** May add Influence dice to Stealth rolls in social situations

#### **Wilderness Ghost** (Focus)
*Prerequisite: Stealth Level 1*

You move through natural environments like a phantom.

- **Natural Camouflage:** Gain +2 dice to Stealth in wilderness environments
- **Trackless:** Leave no trail when moving carefully (double movement time)
- **Animal Empathy:** Animals will not raise alarm unless directly threatened

#### **Master Tracker** (Focus)
*Prerequisite: Observe Level 1*

You can follow trails that others cannot even see.

- **Age Reading:** Determine how old tracks are and predict target's destination
- **Urban Tracking:** Apply tracking skills in cities using different signs
- **Quarry Sense:** Spend 1 Will to gain insight into tracked target's emotional state

#### **Trap Specialist** (Precision)
*Prerequisite: Subtlety Level 2*

You are an expert in mechanical devices and hidden dangers.

- **Trap Sense:** Automatically detect traps on successful Observe rolls
- **Quick Disable:** Reduce trap disarming time by half
- **Improvised Traps:** Create simple traps using available materials

### Social Techniques

#### **Silver Tongue** (Insight)
*Prerequisite: Influence Level 1*

Your words can move hearts and change minds.

- **Emotional Resonance:** Add target's emotional state as bonus dice to Influence rolls
- **Fast Talk:** May use Influence for quick deceptions without preparation
- **Inspiring Presence:** Allies within earshot gain +1 die to resist fear or despair

#### **Intimidating Presence** (Physique)
*Prerequisite: Influence Level 1*

Your mere presence can cow opponents into submission.

- **Physical Menace:** May use Physique instead of Insight for intimidation
- **Demoralizing Strike:** Successful attacks may force morale checks on enemies
- **Reputation:** Word of your fearsome nature precedes you, affecting social encounters

#### **Court Intrigue** (Insight)
*Prerequisite: Influence Level 2*

You navigate the treacherous waters of high society.

- **Social Networks:** Maintain contacts in noble circles for information and favors
- **Etiquette Master:** Never suffer penalties for unfamiliar social situations
- **Political Maneuvering:** Turn social conflicts into advantages through careful positioning

### Magical Techniques

#### **Wyrd Channeler** (Focus)
*Prerequisite: Evoke Level 1*

You have an exceptional connection to Aetheric forces.

- **Efficient Evocation:** Reduce Will costs for Evoke actions by 1 (minimum 1)
- **Wyrd Resonance:** Choose one Wyrd - gain +2 dice when Evoking that specific force
- **Power Surge:** Spend 2 Will to double Wyrd dice generated (increased Mishap risk)

#### **Spell Weaver** (Precision)
*Prerequisite: Weave Level 1*

You excel at shaping raw Aetheric power into precise effects.

- **Delicate Control:** Reduce Complexity of Weave actions by 1 (minimum 0)
- **Sustained Magic:** Maintain concentration on multiple effects simultaneously
- **Efficient Shaping:** May spend Stamina instead of Will for Weave actions

#### **Aetheric Sight** (Insight)
*Prerequisite: Scry Level 1*

Your perception extends beyond the physical realm.

- **Magic Detection:** Automatically sense magical auras and effects
- **Veil Reading:** Understand the nature and strength of magical phenomena
- **Prophetic Glimpses:** Spend 2 Will to gain brief visions of immediate future events

---

## Tier 3: Masteries (Deep Specialization)

*Cost: 3 SP each*

Masteries represent deep expertise with specific tools, techniques, or applications.

### Weapon Masteries

#### **Sword Saint**
*Prerequisite: Light Melee Level 2 OR Dual Wielding*

- **Perfect Balance:** Swords gain +1 to both Offense and Parry
- **Blade Harmony:** When using masterwork swords, gain additional +1 die to all sword actions
- **Cutting Mastery:** Sword attacks ignore 1 point of armor DR

#### **Polearm Specialist**
*Prerequisite: Heavy Melee Level 2*

- **Reach Control:** Control area within weapon's reach - enemies suffer penalties entering/leaving
- **Formation Fighter:** Grant adjacent allies +1 die to defensive actions
- **Charge Breaker:** Set weapons against charges for double damage

#### **Crossbow Engineer**
*Prerequisite: Ranged Level 2*

- **Mechanical Mastery:** Reduce crossbow reload time by 1 AP
- **Precision Bolts:** Crossbow attacks gain +1 Pierce
- **Field Maintenance:** Repair and modify crossbows in the field

### Equipment Masteries

#### **Armor Specialist**
*Prerequisite: Heavy Melee Level 1*

**Level 1:** (Cost: 2 SP)
- **Efficient Wear:** Reduce Encumbrance and Fatigue penalties by 1
- **Armor Maintenance:** Repair Sunder conditions in the field

**Level 2:** (Additional 3 SP)
- **Armor Adaptation:** Reduce Encumbrance and Fatigue penalties by 2
- **Sunder Recovery:** Reduce repair costs for armor conditions
- **Environmental Adaptation:** Ignore environmental penalties for armor-wearing

#### **Shield Specialist**
*Prerequisite: Defender*

- **Mobile Defense:** No penalty for using shields while moving
- **Shield Bash:** Use shield as weapon with Damage equal to Cover value
- **Protective Formation:** Extend shield's Cover to adjacent allies

### Exploration Masteries

#### **Lock Breaker**
*Prerequisite: Trap Specialist*

- **Master Keys:** Open most mechanical locks without tools
- **Speed Picking:** Reduce lock picking time to single AP
- **Lock Analysis:** Determine lock quality and origin by examination

#### **Urban Navigator**
*Prerequisite: Urban Shadow*

- **City Knowledge:** Never get lost in urban environments
- **Safe Houses:** Maintain hidden refuges in major cities
- **Street Network:** Access to information through criminal contacts

### Social Masteries

#### **Spymaster**
*Prerequisite: Court Intrigue*

- **Intelligence Network:** Maintain informants across multiple organizations
- **Code Breaking:** Decipher encrypted messages and hidden communications
- **False Identity:** Maintain multiple convincing personas

#### **Battlefield Commander**
*Prerequisite: Intimidating Presence*

- **Tactical Coordination:** Grant allies bonus dice through battlefield commands
- **Rally:** Restore ally morale and remove fear effects
- **Strategic Insight:** Predict enemy movements and counter-strategies

### Magical Masteries

#### **Elemental Adept** [Specific Wyrd]
*Prerequisite: Wyrd Channeler*

- **Elemental Immunity:** Resist effects from your chosen Wyrd
- **Elemental Weapon:** Temporarily imbue weapons with elemental properties
- **Elemental Form:** Briefly take on aspects of your chosen element

#### **Ritual Master**
*Prerequisite: Spell Weaver*

- **Extended Casting:** Perform complex magical workings over time
- **Group Rituals:** Coordinate multiple casters for greater effects
- **Permanent Effects:** Create lasting magical changes (with significant cost)

---

## Domain Context

### Working in Skills Domain

**Before Making Changes:**
1. **Read the Style Guide:** [SellSword_v4_Style_Guide.md](../SellSword_v4_Style_Guide.md)
2. **Check Current Priorities:** [SellSword_v4_TODO.md](../SellSword_v4_TODO.md)
3. **Review Integration Points:** How changes affect Combat, Equipment, and Character Creation
4. **Consider Balance:** Skill power level vs. cost and prerequisites

### Key Formatting Standards

**Skill Descriptions:**
```markdown
#### **[Skill Name]** ([Action Type])
*Prerequisite: [Requirement]*

[Description of skill's purpose and theme]

- **[Mechanical Benefit]:** [Clear description of game effect]
- **[Resource Use]:** [How skill interacts with Stamina/Will]
- **[Integration]:** [How skill works with equipment/other systems]
```

**Technique Format:**
```markdown
#### **[Technique Name]** ([Action Type])
*Prerequisite: [Requirements]*

[Thematic description of what this technique represents]

- **Resource:** [How technique uses Stamina/Will]
- **Mechanical Benefit:** [Clear game effect]
- **Integration:** [How technique works with equipment/other skills]
```

### Terminology Standards

**Skill Terms:**
- **Core Skills:** Basic abilities (15 total)
- **Techniques:** Specialized applications of core skills
- **Masteries:** Advanced capabilities requiring multiple prerequisites
- **Skill Points (SP):** Currency for advancement

**Resource Terms:**
- **Stamina:** Physical energy for actions
- **Will:** Mental energy for focus and magic
- **Action Points (AP):** Turn-based action economy

### Integration Points

**Combat System:**
- **Action Types:** Skills modify Physique/Finesse/Precision/Focus/Insight actions
- **Resource Costs:** Skills may cost Stamina or Will to use
- **Equipment Synergy:** Skills enhance equipment effectiveness
- **Tactical Options:** Skills provide new combat maneuvers

**Equipment System:**
- **Weapon Skills:** Enhance equipment effectiveness
- **Crafting Skills:** Create and maintain equipment
- **Combat Techniques:** Modify how equipment works

**Character Creation:**
- **Starting Skills:** 7 SP distributed among available skills
- **Advancement:** Skills improve through play and training
- **Prerequisites:** Advanced skills require foundational skills

### Balance Considerations

**Cost vs. Benefit:** More powerful skills should cost more SP
**Prerequisites:** Advanced abilities require foundational skills
**Resource Costs:** Powerful abilities should have meaningful costs
**Advancement Pacing:** Skills should feel rewarding to improve

### Content Templates

**New Skill Template:**
```markdown
#### **[Skill Name]** ([Action Type])
*Prerequisite: [Requirements or "None"]*
*Cost: [SP cost for each level]*

[Thematic description of what this skill represents]

**Level 1:** [Basic mechanical benefit]
**Level 2:** [Enhanced capability]
**Level 3:** [Advanced mastery effect]

**Techniques Available:**
- **[Technique Name]:** [Specialized application]

**Equipment Synergy:** [How this skill works with relevant gear]
**Example:** [Clear scenario showing skill use]
```

---

## Cross-References

### Related Systems
- **Combat Mechanics:** See [Combat README](../Combat/README.md) for skill applications in combat
- **Equipment:** See [Equipment README](../Equipment/README.md) for skill-equipment interactions
- **Character Creation:** See [Characters README](../Characters/README.md) for skill selection and advancement
- **Magic System:** See [Magic README](../Magic/README.md) for magical skill requirements

### Skills Integration
- **Action Enhancement:** Skills add dice to relevant actions
- **Resource Management:** Skills provide new ways to use Stamina and Will
- **Equipment Synergy:** Skills enhance equipment effectiveness
- **Tactical Options:** Skills provide new approaches to challenges

---

*This framework provides the foundation for creating distinct sellsword archetypes while maintaining tactical depth and meaningful choices.*

**Version:** 4.0 (Archived)
**Status:** Replaced by streamlined framework
