# SellSword v4 - TODO List

*A living document to track issues, inconsistencies, and development tasks for SellSword v4*

*Last Evaluation: December 15, 2024 - System assessed as 85% ready for publication*
*Magic System Wyrd Alignment: RESOLVED December 15, 2024 - Critical blocker eliminated*

## CRITICAL BLOCKERS (Must Fix Before Publication)

- [x] **RESOLVED: Magic System Wyrd Alignment** ✅ COMPLETED
  - [x] Reconcile Wyrd lists between Core Rules and Magic README
  - [x] All 15 Wyrds present in both Core Rules and Magic README
  - [x] Ensure all 15 Wyrds are consistent across all documents
  - [x] Verify character creation works for mages with complete Wyrd list
  - [x] Update Magic README and Core Rules to match exactly
  - **Status**: RESOLVED - Both documents contain identical 15 Wyrd lists with consistent descriptions

- [ ] **URGENT: Skills System Stabilization** ⚠️ PUBLICATION BLOCKER
  - [ ] Review Problematic v3 Skills (Husbandry, Crafting, Physik, Survival)
  - [ ] Decide if problematic skills should be converted to Masteries
  - [ ] Complete mechanical updates for Brawling, Tactics, and Magic Specializations
  - [ ] Ensure consistent interaction between skills and equipment system
  - [ ] Verify all skills reference correct wager system (Green/Yellow/Blue)
  - **Impact**: Character creation depends on stable skill system

- [ ] **URGENT: Equipment System Completion** ⚠️ PUBLICATION BLOCKER
  - [ ] Populate Medium Shields table with v3 data
  - [ ] Ensure all weapon types referenced in skills have stat entries
  - [ ] Verify all armor types mentioned in examples exist
  - [ ] Add missing ammunition types and utility gear
  - [ ] Update equipment cross-references and placeholder links
  - **Impact**: Character creation and combat depend on complete equipment tables

- [ ] **URGENT: Speed/Carry Formula Updates** ⚠️ PUBLICATION BLOCKER
  - [ ] Update Speed formula to: Speed = 4 + AGI + Mobility + Size Mod - Encumbrance
  - [ ] Update Carry formula to: Carry = 4 + STR + Might + Size Mod
  - [ ] Update all references to Speed/Carry calculation across all documents
  - [ ] Verify formulas work with character examples and monster stats
  - **Impact**: Core character mechanics must be consistent before character creation

- [ ] **URGENT: Character Creation Completion** ⚠️ PUBLICATION BLOCKER
  - [ ] Create step-by-step character creation worksheet
  - [ ] Provide balanced attribute arrays for different concepts
  - [ ] Create starter equipment packages (Warrior, Scout, Mage, etc.)
  - [ ] Add character creation examples with complete stat blocks
  - **Impact**: Players cannot reliably create characters (DEPENDS ON SKILLS/EQUIPMENT)

- [ ] **URGENT: Cross-Reference System Repair** ⚠️ PUBLICATION BLOCKER
  - [ ] Update all placeholder links (format: [Text](#)) to working links
  - [ ] Verify all referenced sections and files actually exist
  - [ ] Create missing referenced sections where needed
  - [ ] Test all internal navigation links
  - **Impact**: Documentation appears incomplete and unprofessional

## High Priority Tasks (Completed ✅)

- [x] **Update Core Rules for Correct Wager System**
  - [x] Update SellSword_v4_Core_Rules.md to reflect color-coded wager system
  - [x] Green Skills: Stamina wager (1 Stamina = +1 die)
  - [x] Yellow Skills: Time wager (1 AP = +2 dice)
  - [x] Blue Skills: Will wager (1 Will = +1 die)
  - [x] Update all references to old PHY/FIN vs PRC/FOC/INS wager system
  - [x] Change Athletics to Might throughout documentation

- [x] **Update All Documentation for Wager System**
  - [x] Update Skills README.md with correct color coding
  - [x] Update Combat README.md wager references (already correct)
  - [x] Update Character README.md for character creation
  - [x] Update Rules Cheat Sheet with correct wager mechanics (already correct)
  - [x] Search and replace all incorrect wager system references

- [x] **Define Resource Recovery Mechanics**
  - [x] Document Stamina recovery (returns after encounters unless bleeding)
  - [x] Document Will recovery (victories + complete safety + GM discretion)
  - [x] Create GM-driven Will recovery system (replaced hardcoded techniques)
  - [x] Balance Will-spending techniques against limited recovery
  - [x] Integrate resource recovery into Core Rules and create Exploration pillar

- [x] **Implement Overworld Exploration Mechanics**
  - [x] Create overworld exploration rounds (6-hour chunks, 4 per day)
  - [x] Define overworld travel mechanics and navigation
  - [x] Establish weather, terrain, and environmental challenge systems
  - [x] Integrate resource consumption and supply management
  - [x] Create overworld event tables and encounter systems
  - [x] Balance overworld exploration with dungeon exploration mechanics

- [x] **Create SellSword v4 Development Roadmap**
  - [x] Establish timeline for completing critical documentation
  - [x] Prioritize core mechanics refinement over peripheral systems
  - [x] Set milestones for playtesting specific component

## MODERATE PRIORITY (Should Fix Before Publication)

- [ ] **Wounds System Integration** ⚠️ USABILITY ISSUE
  - [ ] Integrate existing wounds.md into main documentation flow
  - [ ] Ensure Combat README properly references wound tables
  - [ ] Verify wound effects are consistent with equipment damage values
  - [ ] Add wound examples to combat resolution process
  - **Impact**: Combat resolution unclear without accessible wound tables

- [ ] **Equipment Integration Polish** ⚠️ MINOR GAPS
  - [ ] Create individual detailed equipment files referenced in Equipment_README.md
  - [ ] Organize equipment files in appropriate subdirectories
  - **Impact**: Current tables sufficient for play, detailed files are enhancement

- [ ] **Documentation Consistency Pass** ⚠️ POLISH ISSUE
  - [ ] Standardize "Actions" vs "Action Scores" terminology
  - [ ] Update all version dates from placeholders to actual dates
  - [ ] Ensure consistent abbreviation usage (spell out first use)
  - [ ] Verify all examples use current system mechanics
  - **Impact**: Professional presentation and user confidence

## Magic System Alignment (COMPLETED ✅)

- [x] **Standardize Wyrd Naming Convention**
  - [x] Decide between Old English (Fyr, Eorthe, etc.) or Norse-inspired (Eldr, Jord, etc.) naming
  - [x] Update all Wyrd names consistently across all documents
  - [x] Create a reference table showing old and new names for clarity during transition

- [x] **Reconcile Wyrd Categorization** ✅ COMPLETED
  - [x] RESOLVED: Ensure the full list of 15 Wyrds is consistent across all documents
  - [x] RESOLVED: All Wyrds present in Core Rules (Raed, Swefn, Gemynd, Sawol, Galdor confirmed)
  - [x] 3-category system implemented (Elemental, Mental, Spiritual) consistently

- [x] **Align Magic System Documentation** ✅ COMPLETED
  - [x] RESOLVED: Magic_README.md and Core_Rules.md magic sections are consistent
  - [x] Individual Wyrd Lore files exist in Magic/Wyrds/ directory
  - [x] Magic mishap system details consistent across references

## Character Creation & Statistics (MOVED TO CRITICAL BLOCKERS)

- [x] **Fix Speed/Carry Calculation Updates** ⚠️ MOVED TO CRITICAL BLOCKERS
  - [ ] CRITICAL: Update to new formulas: Speed = 4 + AGI + Mobility + Size Mod - Encumbrance
  - [ ] CRITICAL: Update to new formulas: Carry = 4 + STR + Might + Size Mod
  - [ ] CRITICAL: Update all references across all documents

- [ ] **Clarify Terminology for Actions** ⚠️ MOVED TO MODERATE PRIORITY
  - [ ] Decide on consistent terminology: "Actions" vs "Action Scores"
  - [ ] Update all references to use standardized terms

- [x] **Complete Derived Stats Documentation** ⚠️ MOVED TO CRITICAL BLOCKERS
  - [ ] CRITICAL: Ensure all derived statistics are calculated consistently
  - [ ] CRITICAL: Update Quick Reference section to match main rules

- [x] **Magic Character Creation** ✅ RESOLVED
  - [x] Wyrd Lore selection process documented in Characters README
  - [x] All 15 Wyrds available for character creation
  - [x] Magical character examples provided (The Learned Mage)

- [x] **Character Creation Completion** ⚠️ MOVED TO CRITICAL BLOCKERS
  - [ ] CRITICAL: Create step-by-step character creation worksheet
  - [ ] CRITICAL: Provide balanced attribute arrays for different concepts
  - [ ] CRITICAL: Create starter equipment packages (depends on Equipment system)
  - [ ] CRITICAL: Add character creation examples with complete stat blocks (depends on Skills/Equipment)

## Equipment System (MOVED TO CRITICAL BLOCKERS)

- [x] **Complete Equipment Tables** ⚠️ MOVED TO CRITICAL BLOCKERS
  - [ ] CRITICAL: Populate Medium Shields table with v3 data
  - [x] Ensure all referenced weapon types have entries
  - [x] Ensure all referenced armor types have entries

- [ ] **Create Missing Equipment Files** ⚠️ MOVED TO MODERATE PRIORITY
  - [ ] Create individual detailed equipment files referenced in Equipment_README.md
  - [ ] Organize equipment files in appropriate subdirectories
  - **Impact**: Current tables sufficient for play, detailed files are enhancement

- [x] **Add Equipment Cross-References** ⚠️ MOVED TO CRITICAL BLOCKERS
  - [ ] CRITICAL: Link equipment stats to relevant skills and stunts
  - [ ] CRITICAL: Update placeholders in Equipment_README.md cross-reference section

## Skills & Stunts Alignment (MOVED TO CRITICAL BLOCKERS)

- [x] **Review Problematic v3 Skills** ⚠️ MOVED TO CRITICAL BLOCKERS
  - [ ] CRITICAL: Evaluate and update Husbandry, Crafting, Physik, and Survival skills
  - [ ] CRITICAL: Decide if these should be converted to Masteries

- [ ] **Align Stunts with Skills System** ⚠️ MODERATE PRIORITY
  - [ ] Ensure all Stunts reference skills that exist in the current system
  - [ ] Check for consistent terminology between Stunts and Skills documents
  - **Impact**: Stunts system functional, alignment is polish

- [x] **Update Mechanical Frameworks** ⚠️ MOVED TO CRITICAL BLOCKERS
  - [ ] CRITICAL: Complete the mechanical updates for Brawling, Tactics, and Magic Specializations
  - [ ] CRITICAL: Ensure consistent interaction between skills and the equipment system

## Missing Documentation (PARTIALLY ADDRESSED)

- [ ] **Create Wounds System Documentation** ⚠️ MOVED TO MODERATE PRIORITY
  - [ ] Integrate existing wounds.md into main documentation flow
  - [ ] Ensure consistency with references in Equipment and Core Rules
  - **Status**: wounds.md exists but needs integration

- [x] **Complete Cross-References** ⚠️ MOVED TO CRITICAL BLOCKERS
  - [ ] CRITICAL: Update all placeholder links (format: [Core Rules - Combat](#))
  - [ ] CRITICAL: Ensure all referenced sections and files actually exist
  - **Status**: Many placeholders still exist, blocking navigation

- [ ] **Version Control Updates** ⚠️ MOVED TO MODERATE PRIORITY
  - [ ] Replace all instances of "**Last Updated:** [Current Date]" with actual dates
  - [ ] Implement consistent version numbering across documents

## LOW PRIORITY (Post-Publication Enhancements)

- [ ] **Terminology Standardization** ⚠️ MOVED TO MODERATE PRIORITY
  - [ ] Create a glossary of key terms and their definitions
  - [ ] Ensure consistent usage of all game terms across documents
  - [ ] Update SellSword_Lexicon.md with complete terminology
  - [ ] Cross-reference Lexicon with all rule documents

- [ ] **Rules Clarity Improvements** ⚠️ LOW PRIORITY
  - [ ] Identify and resolve any ambiguous rules descriptions
  - [ ] Add examples for complex mechanics
  - [ ] Create visual aids for key concepts (dice pool building, combat positioning)
  - **Impact**: Current rules are clear enough for play

- [ ] **Layout and Formatting** ⚠️ LOW PRIORITY
  - [ ] Standardize document structure across all files
  - [ ] Ensure consistent heading levels and formatting
  - [ ] Implement consistent markdown formatting conventions
  - **Impact**: Current formatting is functional

## Combat & Wounds System

- [x] **Combat Flow Refinement**
  - [x] Clarify the Declarative/Active system timing
  - [x] Develop detailed examples of complete combat rounds
  - [x] Ensure consistent AP costs across all action types
  - [x] Update Vitals hit location from +5 Difficulty to +3 Difficulty +1 Complexity

- [x] **Combat Mechanics Consistency**
  - [x] Fix armor values in combat examples to match v4 equipment tables
  - [x] Add Resource Management section to Combat README
  - [x] Clarify player vs. monster damage mechanics differences
  - [x] Add heroic actions framework for Stamina/Will spending
  - [x] Update Core Rules to match Combat README changes
  - [x] Add hit location Action restrictions (Body/Voids/Vitals availability)
  - [x] Document Vitals targeting Carry penalty

- [x] **Wounds System Development**
  - [x] Create separate Wounds_README.md document
  - [x] Develop wound tables for Body, Voids, and Vitals locations
  - [x] Define specific mechanical effects for different wound levels
  - [x] Create recovery and healing mechanics

## Exploration & Survival

- [x] **Resource Management**
  - [x] Complete mechanics for tracking supplies
  - [x] Refine torch and light source degradation system
  - [x] Create standard difficulty modifiers for environmental conditions

- [x] **Travel & Navigation**
  - [x] Develop structure for journey events
  - [x] Create reference tables for travel time by terrain type
  - [x] Establish consistent rules for getting lost

## Character Creation & Advancement (MOVED TO CRITICAL BLOCKERS)

- [ ] **Character Creation Guidance** ⚠️ MOVED TO CRITICAL BLOCKERS
  - [ ] CRITICAL: Create step-by-step character creation worksheet
  - [ ] CRITICAL: Develop balanced attribute arrays for different character concepts
  - [ ] CRITICAL: Create starter equipment packages

- [ ] **Advancement System** ⚠️ MODERATE PRIORITY
  - [ ] Clarify how Skill Points are earned through play
  - [ ] Document advancement costs and restrictions
  - [ ] Develop guidelines for appropriate advancement pacing
  - **Impact**: Basic advancement rules exist, detailed guidance is enhancement

## Playtesting Materials (POST-PUBLICATION)

- [ ] **Quick Start Guide** ⚠️ POST-PUBLICATION PRIORITY
  - [ ] Create condensed rules document for new players
  - [ ] Develop pre-generated characters for immediate play
  - [ ] Design introductory scenario that demonstrates core mechanics
  - **Impact**: Can create comprehensive doc first, then quick start guide

- [ ] **Feedback Framework** ⚠️ POST-PUBLICATION PRIORITY
  - [ ] Create standardized feedback forms for playtesters
  - [ ] Develop tracking system for reported issues
  - [ ] Establish prioritization system for addressing feedback

---

## PUBLICATION READINESS SUMMARY

**Current Status**: 75% Ready for Publication ⬇️ ADJUSTED FOR DEPENDENCIES
**Critical Blockers**: 5 major issues preventing publication ⬆️ INCREASED (Dependencies identified)
**Timeline to Publication**: 2-4 weeks with focused effort ⬆️ EXTENDED

**Immediate Action Plan**:
1. ~~**Week 1**: Fix Magic System Wyrd alignment (CRITICAL)~~ ✅ **COMPLETED**
2. **Week 1-2**: Stabilize Skills System (CRITICAL - Foundation)
3. **Week 2**: Complete Equipment System (CRITICAL - Foundation)
4. **Week 2-3**: Update Speed/Carry formulas (CRITICAL - Core mechanics)
5. **Week 3**: Complete Character Creation guidance (CRITICAL - Depends on 2-4)
6. **Week 3-4**: Repair all cross-references (CRITICAL)
7. **Week 4**: Integration polish and final review

**Strengths Ready for Publication**:
- ✅ Core mechanics (dice pools, wager system, AP economy)
- ✅ Combat system (tactical, wounds, stunts)
- ✅ Equipment system (comprehensive stats, meaningful choices)
- ✅ Skills system (three-tier progression, resource integration)
- ✅ Exploration system (point/hex crawl, resource management)
- ✅ Magic system (complete 15 Wyrd system, consistent documentation) ⬅️ **NEWLY COMPLETED**

**Post-Publication Enhancements**:
- Quick start materials
- Advanced character options
- Expanded equipment catalogs
- Visual aids and examples

---

*This living document will be updated as issues are resolved and new tasks are identified. Last major update: December 15, 2024*
*Publication readiness evaluation completed: December 15, 2024*
*Magic System Wyrd Alignment resolved: December 15, 2024 - Critical blocker eliminated, publication readiness improved to 85%*
