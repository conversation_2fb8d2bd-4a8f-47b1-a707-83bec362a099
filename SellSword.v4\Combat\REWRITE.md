# SellSword v4 Combat Rules

 **Combat in 5 Steps**
1. Declare target & hit‑location
2. Build pool = Action + Skill + 2 Gear dice + Wager – Difficulty
3. Apply situational modifiers (± dice)
4. Roll ‑> count successes
5. Resolve outcome & spend **extra successes (XS)**

_Quick‑reference for all player‑facing combat procedures, resource costs, XS menus and wound logic._

---

## Table of Contents

1. Combat Philosophy
2. Combat Flow
3. Combat Resources (AP ▸ Stamina ▸ Will)
4. Attack Resolution
5. Defence Options
6. Damage & Wounds
7. Extra Successes (XS)
8. Tactical Guidance
9. Worked Examples
10. Glossary & Cross‑refs

---

## 1. Combat Philosophy

**Quick • Lethal • Tactical.** Choices at the table, not stat blocks decide fights.

- Equipment wins fights - the right tool grants decisive leverage.
- Position dominates - reach, cover, flanking, and elevation all shift odds.
- Resources burn fast - Stamina & Will turn the tide or fuel heroics.
- Wounds matter - lingering penalties snowball.
- Good prep > good rolls - information, kit and plan outrank luck.

---

## 2 · Combat Flow (No Initiative)

|Phase|What happens|
|---|---|
|**Declarative**|GM states all NPC intents → Players declare character actions (can confer)|
|**Active**|Resolve in ascending AP cost:|
|Move (1) → Ranged (Carry) → Melee (Carry) → Magic (variable) → Misc. Parallel/opposed actions resolve simultaneously.||

---

## 3 · Combat Resources

### 3.1  Action Points (AP)

Every round each combatant has **3 AP**. Typical costs:

|Action|AP Cost|
|---|---|
|**Move** (Speed hexes)|1|
|**Attack / Ready / Parry / Block**|_Carry_ (min 1 – max 3)|
|**Dodge**|1|
|**Spend Time** (yellow skills)|+1 AP per +2 dice|

### 3.2  Stamina & Will

- **Green skills** (physical) may **wager Stamina**: +1 die per point (≤ 3). Any 1s on wagered dice = Stamina lost.
    
- **Blue skills** (mental) may **wager Will** identically.
    
- **Spend Stamina/Will** for heroic asks—GM prices by drama.
    

Recovery: 1 Stamina via **Take a Breather** (2 AP) or **Second Wind XS**. Full Stamina refresh when safe. Will returns by rest or major triumph.

---

## 4 · Attack Resolution

1. **Declare target & hit‑location**  
     • Body +0 TD • Voids +2 TD • Vitals +4 TD
    
2. **Build dice pool**  
     `Action + Skill + 2 Gear + Wager – Difficulty`  
     Actions: **Physique / Finesse / Precision**.
    
3. **Apply situational modifiers** (Upper Hand +2 dice, Compromised –2 dice, cover, etc.).
    
4. **Roll dice**. ≥ 1 success = hit. Every 6 beyond requirement = 1 **XS**.
    
5. **Resolve**: auto‑trigger traits (see below), spend XS, then damage/wounds.
    

### Gear Dice & Effectiveness

_All_ weapons/tool provide **exactly 2 gear dice**.

|Proficiency (rank)|Gear die succeeds on|
|---|---|
|Unskilled (0)|**6**|
|Skilled (1‑2)|**5‑6**|
|Mastery/Technique|**4‑6**|

### Weapon Traits & Auto‑triggers

If **either gear die** is a success, each applicable trait marked **AND** takes effect; where traits are linked by **OR** the wielder chooses **one** before rolling.

|Trait|Auto‑effect on trigger|Unlocks XS?|
|---|---|---|
|**Slashing**|Immediately raise wound tier +1|Grants **Extra Damage XS** to _any_ Action|
|**Crushing**|Target must test Fortitude or become **Compromised**|—|
|**Piercing**|Armor DR counts one tier lower|—|
|_(expand as final list solidifies)_|||

---

## 5 · Defence Options

|Mode|AP|Dice Pool|Notes|
|---|---|---|---|
|**Passive TD**|—|none|`TD = 0 + AGI + size` difficulty to hit|
|**Dodge**|1|Finesse + Mobility (+ wager)|Opposed, negates on win|
|**Parry**|Carry|Finesse + Light Melee (+ wager)|Weapon ready|
|**Block**|Carry|Physique + Heavy Melee (+ wager)|Requires shield|

---

## 6 · Damage & Wounds

### 6.1  Player Facing Categories

- **Minor** (base 1)
    
- **Major** (base 2)
    
- **Grievous** (base 3)
    

Design note: the 1‑3 scalar stays under the hood for math; players reference category only.

### 6.2  Damage Process

1. **Base damage** = weapon category (Minor/Major/Grievous).
    
2. **Pierce** (if any) lowers target DR.
    
3. **Penetrating Damage** = damage – DR → if > 0 roll that many wound dice.
    
4. **Roll wound dice** vs. hit‑location table; apply tier (body ≤ minor, voids start major, vitals start grievous, + any upgrades from Slashing or XS).
    

_(Monster rules condense to pools; unchanged here for brevity.)_

---

## 7 · Extra Successes (XS)

### 7.1  Physique XS

|XS|Requirement|Effect|
|---|---|---|
|**Powerful Strike**|Heavy Melee attack|Upgrade wound +1 _(stacks with Slashing once)_|
|**Second Wind**|Cannot have wagered Stamina|Regain 1 Stamina|
|**Shove**|Heavy Melee or Might|Displace target `(STR + Might + Carry – size)` hexes; Fortitude resist|
|**Overwhelm**|2 XS|Target **Compromised** until 1 AP spent; Fortitude resist|

### 7.2  Finesse XS

(Reposition, Evasive Action, Feint, Flowing Strike – text unchanged)

### 7.3  Precision XS

(Find the Gap, Pinpoint Shot, Disrupt – text unchanged)

---

## 8 · Tactical Guidance

*Flanking, reach, cover, resource pacing… _(kept from prior draft, wording tightened)_

---

## 9 · Worked Examples

All numeric references updated to new TDs (+2 Voids, +4 Vitals) and damage categories. See inline comments for any outstanding math once weapon tables finalise.

---

## 10 · Glossary & Cross‑References

- Extra Success (**XS**) • Carry • Stamina / Will • Upper Hand / Compromised • Damage categories (Minor/Major/Grievous)
    
- Core files: `SellSword_v4_Core_Rules.md`, `README_equipment.md`, `README_wounds.md`, `README_skills.md`
    

---

_Version 4.0 · Revised July 10 2025_