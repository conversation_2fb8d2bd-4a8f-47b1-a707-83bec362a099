# SellSword v4 — Style Guide Addendum (Clean Canon)

Addendum to SellSword_v4_Style_Guide.md capturing canonical baselines, anchor rules, and terminology guardrails introduced with the Clean Core.

## Table of Contents

- [SellSword v4 — Style Guide Addendum (Clean Canon)](#sellsword-v4--style-guide-addendum-clean-canon)
  - [Table of Contents](#table-of-contents)
  - [Scope](#scope)
  - [Canonical Baselines](#canonical-baselines)
  - [Terminology Guardrails](#terminology-guardrails)
  - [Anchor \& Cross-Reference Rules](#anchor--cross-reference-rules)
  - [Abbreviation Policy](#abbreviation-policy)
  - [File \& Section Templates](#file--section-templates)
  - [Deprecation \& Migration Notes](#deprecation--migration-notes)

---

## Scope

This addendum defines non-negotiable numeric baselines and documentation rules that all v4 materials MUST follow. It complements, not replaces, SellSword_v4_Style_Guide.md.

---

## Canonical Baselines

These values are authoritative for v4:

- Actions base = 3 + attribute pair
  - Physique = 3 + STR + AGI
  - Finesse = 3 + AGI + DEX
  - Precision = 3 + DEX + ACU
  - Focus = 3 + ACU + PRE
  - Insight = 3 + PRE + INT

- AP per round = 4

- Wagers
  - Green (Stamina): 1 Stamina = +1 die; lose that Stamina if any 1s on the Stamina-wagered dice
  - Yellow (Time): 1 AP = +2 dice; hard cap +4 dice per action from Time
  - Blue (Will): 1 Will = +1 die; lose that Will if any 1s on the Will-wagered dice
  - Magic mishap trigger: Will dice only

- Targeting and Vitals
  - Body: +0 Difficulty
  - Voids: +3 Difficulty
  - Vitals: +3 Difficulty and +1 Complexity; extra penalty dice equal to weapon Carry unless Precise; generally Precision-only

- Modifiers
  - Upper Hand: +2 dice; at most one instance unless explicitly allowed
  - Compromised: −2 dice; at most one instance unless explicitly allowed

- Derived Statistics
  - Speed = 4 + AGI + Mobility + Size Mod − Encumbrance
  - Carry = 4 + STR + Might + Size Mod
  - Stamina = Physique + Fortitude − Armor Fatigue
  - Will = Focus + Resolve
  - Lore Slots = 3 + INT + Reason
  - Standing Slots = 3 + PRE + Influence

- Carry vs Force (split)
  - Carry: AP handling and encumbrance-facing stat; referenced by some rules (e.g., Vitals penalty)
  - Force: Mass/impact stat for stunts, knockback, Sunder thresholds, and any “applied force” calls
  - If a rule does not explicitly say “Carry,” treat “applied force” semantics as Force

---

## Terminology Guardrails

- Use modern procedural headings; use Lexicon terms primarily in flavor and examples.
- First-use expansions:
  - Action Points (AP), Damage Reduction (DR), Wyrds (magical domains)
- Consistency:
  - “Actions” refers to the five action scores (do not use “Action Scores” interchangeably)
  - “Carry” and “Force” are distinct; never substitute one for the other in prose or tables
- Hit Locations:
  - Always write “Body,” “Voids,” “Vitals” (capitalized in headings or table labels)
- Modifiers:
  - “Upper Hand” and “Compromised” are proper game terms; avoid synonyms in rules text

---

## Anchor & Cross-Reference Rules

- Anchor format: auto-generated from headings using lowercase kebab-case
  - H2 “Core Mechanics” → #core-mechanics
  - H3 “Action Points and Timing” → #action-points-and-timing
- Internal file links:
  - Use `[Text](FileName.md#anchor)` format
  - Example: `[Core Rules – Combat](SellSword_v4_Core_Rules_Clean.md#combat)`
- Cross-file references:
  - Do not use placeholders (#). Always provide a resolving link to a real file and anchor
- Section titles should be stable to avoid anchor churn; if you must rename a section, update all incoming links
- Prefer short, descriptive section names to keep anchors short and predictable

---

## Abbreviation Policy

- First use in a document: spell out with abbreviation in parentheses
  - “Action Points (AP)”
- Subsequent formal text: use full term in sentences; use abbreviations in tables, stat blocks, and quick references
- Approved abbreviations for tables:
  - Str, Agi, Dex, Acu, Pre, Int
  - Phy, Fin, Prc (Precision), Foc, Ins
  - DR, AP, SP (Skill Points)
- Avoid introducing new abbreviations without listing them in the document’s glossary

---

## File & Section Templates

- All new high-level docs should include:
  - H1 Title
  - Italicized one-line purpose
  - Table of Contents (for docs > ~100 lines)
  - Clear sections with H2/H3 hierarchy
  - “Cross-References” section
  - “Glossary” or “Key Terms” section if introducing domain terms
  - Footer with Version and Last Updated (actual date)

- Recommended section order for rules documents:
  1) Overview / Philosophy
  2) Core Mechanics
  3) Domain Mechanics (e.g., Combat, Magic, Equipment)
  4) Examples (optional but encouraged)
  5) Quick Reference or Procedures
  6) Cross-References
  7) Glossary / Key Terms

---

## Deprecation & Migration Notes

- Legacy files that conflict with this addendum MUST display a “Deprecated in favor of Clean Core” banner at top, linking to the Clean documents:
  - SellSword_v4_Core_Rules_Clean.md
  - SellSword_v4_Rules_Cheat_Sheet_Clean.md
  - This Addendum

- Migration guidance:
  - Replace any “Actions = 2 + pair” or “AP = 3” language with the canonical baselines above
  - Replace any mixed or ambiguous references to Carry with explicit Carry or Force per rule intent
  - Ensure all combat targeting texts reflect current Vitals penalty (−Carry dice unless Precise)
  - Normalize Wagers to include the Yellow cap (+4 dice per action)

- Cross-reference cleanup:
  - Run a pass to ensure all `[...](#)` placeholders are resolved to concrete anchors
  - Verify that “Wounds” references link to the actual Wounds guide once integrated

---

This addendum is binding for all SellSword v4 materials. If a document conflicts with this addendum, the addendum rules take precedence.

**Version:** 4.0  
**Last Updated:** August 4, 2025
