Below is a *designer-to-designer* critique of **SellSword v4** with special focus on the proposed **Universal Equipment Dice System** in *Equipment\_README\_v2*. I treat the current *Core Rules* file as the baseline and note every spot where the new equipment philosophy either shines or collides with the existing chassis.

---

## 1. Big-Picture Strengths

| Feature                                  | Why it Already Works                                                                                                       |
| ---------------------------------------- | -------------------------------------------------------------------------------------------------------------------------- |
| **Action + Skill + Equipment dice pool** | Easy to teach (“add the coloured piles”) yet still lets you tune any factor independently.                                 |
| **Wager system (Stamina / Time / Will)** | Elegant risk-reward loop that naturally differentiates archetypes and keeps resource tracking meaningful without HP bloat. |
| **AP economy tied to *Carry***           | Folds encumbrance, reach and tempo into one visible number—players *feel* weapon heft during play.                         |
| **Torch / Exploration clock**            | Turns light into a de facto countdown and encounter generator; dovetails with gritty tone.                                 |

You already have the *bones* of a tight, lethal, tactical game. The only area that still wobbles is gear differentiation once you switch to “2 dice universal”.

---

## 2. Universal Equipment Dice System – What It Fixes

### 2.1 Complexity & Speed

*Counting only two equipment dice* ends the endless “Off + Block + Parry” fiddling and keeps turn math as:

```
Action Dice (3-6) + Skill Dice (0-3) + 2 Equipment Dice ± Situational
```

### 2.2 On-Ramp for New PCs

Using skill-gated *success thresholds* (6-only → 5-6 → 4-6) punishes newbies less than a flat “no skill no dice” model; they still roll 2 dice and occasionally spike a 6.

### 2.3 Future-Proofing

Because every tool follows the same dice behaviour, you can add new weapon traits, gadgets and odd artefacts without re-balancing the whole table of numeric bonuses.

---

## 3. Critical Friction Points

### 3.1 **Core Rules Still Reference the Old Stats**

*SellSword\_v4\_Core\_Rules.md* (and deprecated README) rely on:

* **Offense** dice in the attack pool
* **Block / Parry** dice for defense
* The hit location table invokes “Weapon Offense – Difficulty”

With the new system those terms disappear. Unless you scrub them, *two incompatible rulesets* will coexist and confuse GMs.

> **Fix**: Replace every “+ Weapon Offense” reference with “+2 Equipment Dice”. Where Block/Parry appear, annotate that they are simply *specialisations* which let the 2 dice enter the pool for that action.

---

### 3.2 **Weapon Identity Risks Flattening**

Two daggers and a greatsword now provide the same *to-hit* support (2 dice). Their differences shrink to **Damage / Reach / Pierce / Carry**.

* That may be *enough*—but test edge cases:

  * **Daggers vs plate armour**: They cap at *Pierce 0* and *Damage 2*. Against DR 3 they can never harm the foe, success pool or not. Good!
  * **Agile vs slow weapons**: AP cost still splits them.

Still, remember that players often equate *accuracy* with *weapon quality*. If you want “warhammer hits less often but smashes armour when it lands” keep it, but be sure the trade-off feels visceral in playtest.

**Low-overhead tweak**
Bring back a *single* qualitative accuracy flag rather than numerical Offense:

```markdown
| Weapon | Accuracy Tag | Notes |
|--------|--------------|-------|
| Rapier | Precise      | May re-roll one failed equipment die vs Unarmoured targets |
| Flail  | Wild         | −1 Threshold (needs 5-6 even for master) but deals +1 Force |
```

A tag keeps weight low yet differentiates blades from clubs.

---

### 3.3 **Equipment Dice Out-shine Skill Dice at High Tiers**

*Probability snapshot (2 d6):*

| Threshold       | Avg successes | ≥1 success chance |
| --------------- | ------------- | ----------------- |
| 6-only (Novice) | **0.33**      | 30.6 %            |
| 5-6 (Skilled)   | **0.67**      | 55.6 %            |
| 4-6 (Master)    | **1.00**      | 75.0 %            |

A master’s 2 equipment dice will yield one clean hit **three times as often** as a novice’s—and that’s before action + skill dice. Great! *But* note that **skill dice always need a 6**; past L2 a character’s hit rate is driven more by gear proficiency than by core talent. If that is intentional (SellSword is gear-centric) leave it. If not, consider unlocking 5-6 thresholds for *Skill Dice* at Skill 3 to keep mastery feeling broad, not purely equipment-bound.

---

### 3.4 **Durability “Double-1s” Rule Feels Abstract for Heavy Use**

Chance of rolling snake-eyes on 2d6 = **2.78 %** per action—identical for wood club and adamant hammer. That fits “universal”, yet big Force weapons cracking against full plate *should* wear faster.

*Minimal solution*: state that **if the action involves Force ≥ 2, durability loss triggers on *any* two 1s *or* one 1 + one 2**. Adds heft without extra dice.

This is because you aren't taking into account gear has different durability ratings (1-5). They don't 'break' on double 1's but rather lose 1 point of durability. They also lose more durability if attacking something with high DR. I also think we can tag weapons and armor with something like fragile where they lose durability on 1's and 2's. Which brings the chance up to something like 10%.

---

## 4. Knock-On Updates You’ll Need

1. **Pool Formula** – In every doc:

   ```diff
   - Base Pool = Action + Skill + Equipment Dice + Wager − Difficulty
   + Base Pool = Action + Skill + 2 Equipment Dice + Wager − Difficulty
   ```
2. **Attack Resolution** – Remove “Weapon Offense”.
3. **Defence** – Clarify that *if* the weapon/shield is flagged for *Block* or *Parry*, its 2 dice may be added to the defence pool; otherwise you roll without equipment aid.
4. **Character Sheets** – Replace numeric Off/Blk/Par columns with checkboxes for *Specialisation tags* plus the static gear dice.
5. **NPC / Monster Sheets** – Same tidy-up; helps reduce GM prep time.

---

## 5. Minor Editorial / Consistency Notes

| Section                    | Issue                                                                                                                                                                                                                        | Suggestion                                              |
| -------------------------- | ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | ------------------------------------------------------- |
| Core Rules §“Wound System” | “Penetrating damage determines number of *d6* rolled” – now ambiguous; weapons all have Dmg 1-3.                                                                                                                             | Spell out: *Roll 1 d6 per point of penetrating damage.* |
| Equipment tables           | Some list **Pierce** column, others **Prc**; unify.                                                                                                                                                                          |                                                         |
| Universal README           | Durability damage phrase “equal to the higher of: 1 point, or the DR value of what you attacked” – armour DR might be 3 but you blocked instead of attacked. Clarify that for blocks it uses the incoming weapon’s *Damage*. |                                                         |
| Core vs README             | Size modifier appears in derived stats but is not defined. Add a quick reference (+1 Large, −1 Small).                                                                                                                       |                                                         |

You are conflating wound rules for players and monsters. Only players roll on wound tables and roll d6 equal to penetrating damage.

---

## 6. Recommended Next Experiments

1. **Micro-playtest**
   *Three* identical PCs with Light, Medium, Heavy weapons; pit them against a DR 2 brigand and record:

   * hits/round,
   * time to first durability loss,
   * average AP spent per kill.
     You will feel instantly whether 2 dice is flattening too much.

2. **Skill-only Contest**
   Run lock-picking with and without lockpicks for Skill 0–3 characters. Make sure the “tool but no skill” success odds stay *interesting yet risky*.

3. **Edge-Case Audit**
   Crossbows (no melee Block tag) – do they get *any* equipment dice to dodge in close? If not, that neatly models awkwardness; just state it outright.

---

## 7. Verdict

The **Universal Equipment Dice** overhaul is a *cleaner, faster* direction that fits SellSword’s “gear matters” thesis. It:

* Speeds turns,
* Gives novices a fighting chance,
* Lets mastery shine through threshold gating.

To land perfectly you only need to:

1. **Purge legacy Offense/Block language** and align every document to the new model.
2. **Re-inject weapon personality** through light-touch tags (or similar) so swords still *feel* handier than hammers.
3. **Stress-test durability and success curves** to catch any emergent flat-spots.
