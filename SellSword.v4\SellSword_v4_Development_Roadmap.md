# SellSword v4 Development Roadmap

*Strategic timeline and milestones for completing SellSword v4*

## Overview

This roadmap prioritizes **core mechanics refinement** over peripheral systems to achieve a **playable, testable game** as quickly as possible. The focus is on completing critical documentation needed for playtesting while maintaining high quality standards.

---

## Current Status Assessment

### ✅ **Completed Systems (Ready for Playtesting)**
- **Core Rules**: Wager system, basic mechanics, dice resolution ✅
- **Combat System**: Tactical combat, wounds, stunts ✅
- **Exploration System**: Overworld travel, resource management, events ✅
- **Equipment System**: Weapons, armor, basic gear ✅

### 🔄 **Partially Complete Systems (Need Refinement)**
- **Magic System**: Core mechanics exist, needs Wyrd alignment
- **Skills System**: Framework complete, needs problematic skill review
- **Character Creation**: Basic system exists, needs templates and guidance

### ❌ **Missing Critical Components**
- **Wounds System Documentation**: Exists but needs integration
- **Cross-References**: Many placeholder links need updating
- **Playtesting Materials**: Quick start guide, pre-gen characters

---

## Phase 1: Core Playability (Weeks 1-4)

**Goal**: Complete minimum viable game for initial playtesting

### Week 1: Magic System Alignment
- [ ] **Reconcile Wyrd Categorization** (Priority: HIGH)
  - Decide between 3-category vs 2-category system
  - Resolve missing Wyrds in Core Rules
  - Ensure 15 Wyrds are consistent across documents

- [ ] **Align Magic Documentation** (Priority: HIGH)
  - Sync Magic_README.md with Core_Rules.md
  - Standardize magic mishap system details

### Week 2: Character System Completion
- [ ] **Fix Speed Calculation Inconsistency** (Priority: HIGH)
  - Standardize formula across all documents
  - Update Quick Reference section

- [ ] **Character Creation Guidance** (Priority: MEDIUM)
  - Create step-by-step worksheet
  - Develop balanced attribute arrays
  - Create starter equipment packages

### Week 3: Skills & Equipment Integration
- [ ] **Review Problematic v3 Skills** (Priority: MEDIUM)
  - Evaluate Husbandry, Crafting, Physik, Survival
  - Decide conversion to Masteries where appropriate

- [ ] **Complete Equipment Tables** (Priority: LOW)
  - Populate Medium Shields table
  - Ensure all referenced items have entries

### Week 4: Documentation Polish
- [ ] **Complete Cross-References** (Priority: HIGH)
  - Update all placeholder links
  - Verify all referenced sections exist

- [ ] **Create Wounds System Documentation** (Priority: MEDIUM)
  - Integrate existing wounds.md into system
  - Ensure consistency with Equipment/Core Rules

**Phase 1 Milestone**: Complete, internally consistent game ready for closed playtesting

---

## Phase 2: Playtesting Preparation (Weeks 5-8)

**Goal**: Create materials needed for effective playtesting

### Week 5-6: Quick Start Materials
- [ ] **Quick Start Guide** (Priority: HIGH)
  - Condensed rules for new players
  - Essential mechanics only
  - Clear examples and procedures

- [ ] **Pre-Generated Characters** (Priority: HIGH)
  - 4-6 ready-to-play characters
  - Different archetypes and playstyles
  - Complete equipment and backstories

### Week 7-8: Playtesting Framework
- [ ] **Introductory Scenario** (Priority: HIGH)
  - Demonstrates core mechanics
  - Combat, exploration, and social elements
  - 2-3 hour session length

- [ ] **Feedback Framework** (Priority: MEDIUM)
  - Standardized feedback forms
  - Issue tracking system
  - Prioritization guidelines

**Phase 2 Milestone**: Complete playtesting package ready for external testing

---

## Phase 3: Refinement & Polish (Weeks 9-12)

**Goal**: Address playtesting feedback and complete remaining systems

### Weeks 9-10: Core Refinements
- [ ] **Address Critical Feedback** (Priority: HIGH)
  - Fix game-breaking issues
  - Clarify confusing rules
  - Balance mechanical problems

- [ ] **Terminology Standardization** (Priority: MEDIUM)
  - Complete SellSword_Lexicon.md
  - Ensure consistent usage across documents

### Weeks 11-12: Final Polish
- [ ] **Advanced Character Options** (Priority: LOW)
  - Advancement system details
  - Advanced equipment options
  - Optional rules and variants

- [ ] **Layout and Formatting** (Priority: LOW)
  - Standardize document structure
  - Consistent markdown formatting
  - Visual aids for complex concepts

**Phase 3 Milestone**: Polished, tested game ready for broader release

---

## Critical Path Analysis

### **Blocking Dependencies**
1. **Magic System Wyrd Alignment** → Affects character creation and equipment
2. **Speed Calculation Fix** → Affects all character stats and combat
3. **Cross-Reference Completion** → Affects usability of all documents

### **Parallel Development Tracks**
- **Documentation Polish** can proceed alongside **System Refinement**
- **Playtesting Materials** can be developed while **Core Systems** are finalized
- **Equipment Tables** completion is independent of other systems

### **Risk Mitigation**
- **Weekly Reviews**: Assess progress and adjust priorities
- **Minimum Viable Product**: Focus on playability over completeness
- **Feedback Integration**: Build testing into development cycle

---

## Success Metrics

### **Phase 1 Success Criteria**
- [ ] All core systems have complete, consistent documentation
- [ ] No placeholder links in critical game mechanics
- [ ] Character creation produces viable, balanced characters
- [ ] Combat system resolves conflicts clearly and quickly

### **Phase 2 Success Criteria**
- [ ] New players can learn and play within 30 minutes
- [ ] Playtesting sessions run smoothly without rules confusion
- [ ] Feedback collection captures actionable improvement data

### **Phase 3 Success Criteria**
- [ ] Game balance feels fair and engaging
- [ ] Documentation is professional and comprehensive
- [ ] System supports long-term campaign play

---

## Resource Allocation

### **High Priority** (60% of effort)
- Magic system alignment
- Cross-reference completion
- Quick start materials
- Critical feedback integration

### **Medium Priority** (30% of effort)
- Character creation guidance
- Wounds system integration
- Terminology standardization
- Playtesting framework

### **Low Priority** (10% of effort)
- Equipment table completion
- Advanced character options
- Layout and formatting
- Optional rules development

---

*This roadmap emphasizes rapid iteration toward a playable game while maintaining SellSword v4's high quality standards. Regular reviews will adjust priorities based on development progress and playtesting feedback.*

**Version:** 1.0  
**Created:** December 15, 2024  
**Next Review:** Weekly during active development
