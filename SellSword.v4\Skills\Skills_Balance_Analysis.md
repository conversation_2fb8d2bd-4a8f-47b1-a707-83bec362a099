# Skills System Evolution Analysis & Comprehensive Refinement Plan

*Complete analysis of SellSword skill system evolution (v1-v4) with definitive path forward*

**Purpose:** Resolve critical skills system gaps and establish final v4 framework

---

## Executive Summary

After comprehensive analysis of all SellSword versions (v1-v4), the current v4 skills system has **critical gaps** that prevent character creation for essential archetypes. The system has strong foundational design but is **incomplete** - missing core skills that existed in previous versions.

### Critical Issues Identified
1. **Missing Essential Skills** - Core character concepts cannot be created (healers, crafters, unarmed fighters, commanders)
2. **Incomplete Coverage** - v4's 15 skills don't cover the breadth that v2/v3 provided
3. **Lost Functionality** - Previous versions had comprehensive skill coverage that v4 abandoned
4. **Publication Blocker** - Character creation is impossible for many concepts

---

## Historical Evolution Analysis

### V1 System (Broad Skills + Lore)
**Philosophy:** "Fewer, broader skills" with separate Lore system
**Core Skills:** 11 broad skills covering all character concepts
- **Combat:** Melee Combat, Ranged Combat, Mounted Combat
- **Physical:** Athletics
- **Social:** Social Influence
- **Utility:** Subterfuge, Survival, Physik, Alchemy, Engineering, Tactics
- **Lore System:** Separate knowledge specializations (Woodcraft, Politics, etc.)

**Strengths:** Complete coverage, clear skill/lore distinction
**Weaknesses:** Too broad, lacked mechanical differentiation

### V2/V3 System (Category/Style/Specialization)
**Philosophy:** Three-tier progression with mechanical innovation
**Structure:**
- **Tier 1 (Category):** 17 foundational skills with dice bonuses
- **Tier 2 (Style):** Specialized approaches with unique mechanics
- **Tier 3 (Specialization):** Deep mastery with specific benefits

**Complete v2/v3 Category Skills:**
1. **Athletics** (PHY/FIN) - Physical prowess, movement
2. **Brawling** (PHY/FIN) - Unarmed combat, grappling
3. **Weapons Craft** (PHY/FIN/PRC) - Armed melee combat
4. **Ranged Combat** (PRC) - Bows, crossbows, thrown weapons
5. **Husbandry** (FIN/INS) - Animal handling, riding
6. **Subterfuge** (FIN/PRC) - Stealth, locks, traps
7. **Survival** (FRT/FCS) - Wilderness skills, tracking
8. **Crafting** (PRC/INT) - Creating/repairing items
9. **Influence** (RES/INS) - Social manipulation, leadership
10. **Physik** (PRC/INT) - Medicine, healing, anatomy
11. **Tactics** (FCS/INS) - Battlefield strategy, command
12. **Discipline** (FCS/RES/INS) - Mental fortitude, focus
13. **Evoke** (RES) - Gathering magical power
14. **Weave** (FCS) - Shaping magical effects
15. **Scribe** (PRC) - Binding magic to objects
16. **Scry** (INS) - Magical perception, divination

**Strengths:** Comprehensive coverage, clear progression, mechanical innovation
**Weaknesses:** Complex cost structure, some redundancy

### V4 System (Core/Techniques/Masteries)
**Philosophy:** Streamlined foundation with dramatic playstyle differentiation
**Current State:** 15 Core Skills + Techniques + Masteries
- **Missing:** Brawling, Tactics, Physik, Crafting, Husbandry, Survival (partially), Discipline
- **Renamed:** Athletics → Might, Discipline → Resolve, Scribe → removed

**Critical Gap:** Essential character archetypes cannot be created

## Character Archetype Coverage Analysis

### V2/V3 Comprehensive Coverage
**Combat Archetypes:**
- ✅ **Armed Fighter:** Weapons Craft + Styles (Dueling, Two-Handed, Shield Bearer)
- ✅ **Unarmed Fighter:** Brawling + Styles (Grappler, Striker)
- ✅ **Archer:** Ranged Combat + Styles (Marksman, Skirmisher)
- ✅ **Mounted Warrior:** Husbandry + Styles (Rider, Cavalry Tactics)
- ✅ **Battlefield Commander:** Tactics + Styles (Commander, Strategist)

**Utility Archetypes:**
- ✅ **Healer:** Physik + Styles (Surgeon, Diagnostician)
- ✅ **Crafter:** Crafting + Styles (Smith, Artisan, Alchemist)
- ✅ **Scout:** Survival + Styles (Tracker, Forager, Pioneer)
- ✅ **Infiltrator:** Subterfuge + Styles (Infiltrator, Saboteur, Deceiver)
- ✅ **Social Manipulator:** Influence + Styles (Orator, Interrogator, Manipulator)

**Magical Archetypes:**
- ✅ **Battle Mage:** Evoke + Weave + Combat Skills
- ✅ **Enchanter:** Scribe + Crafting synergy
- ✅ **Diviner:** Scry + Investigation focus
- ✅ **Focused Caster:** Discipline + Magic Skills

### V4 Current Gaps
**Missing Core Archetypes:**
- ❌ **Unarmed Fighter:** No Brawling skill
- ❌ **Healer:** No Physik skill
- ❌ **Crafter:** No Crafting skill
- ❌ **Battlefield Commander:** No Tactics skill
- ❌ **Animal Handler:** No Husbandry skill
- ❌ **Focused Warrior:** No Discipline/mental training skill

**Partial Coverage:**
- ⚠️ **Scout:** Survival functions split across Observe/Fortitude/Mobility
- ⚠️ **Enchanter:** No Scribe equivalent (removed from v4)

### V4 Design Philosophy Alignment

**V4 Core Principles:**
- **Action Types:** Physique/Finesse/Precision/Focus/Insight (3 + 2 attributes each)
- **Wager System:** Green (Stamina), Yellow (Time), Blue (Will)
- **Mechanical Innovation:** Techniques provide unique capabilities, not just dice
- **Resource Integration:** Skills enable novel Stamina/Will usage
- **Equipment Synergy:** Advanced skills interact meaningfully with gear

---

## Definitive Solution: Expanded Core Skills Framework

### Core Skills Expansion (15 → 20)

**Add Missing Essential Skills to Core Tier:**

#### **New Core Skill: Brawling** (Green - Stamina Wager)
*Action Type: Physique*
- **Function:** Unarmed combat, grappling, improvised weapons
- **Level Benefits:** +1 die per level to unarmed attacks, grapples, shoves
- **Secondary:** +1 to unarmed damage per level
- **Wager:** Spend Stamina for +1 die each (risk loss on 1s)

#### **New Core Skill: Tactics** (Blue - Will Wager)
*Action Type: Insight*
- **Function:** Battlefield awareness, command, strategic thinking
- **Level Benefits:** +1 die per level to tactical assessments, command actions
- **Secondary:** +1 to initiative per level
- **Wager:** Spend Will for +1 die each (risk loss on 1s)

#### **New Core Skill: Physik** (Yellow - Time Wager)
*Action Type: Precision*
- **Function:** Medicine, healing, anatomy, diagnosis
- **Level Benefits:** +1 die per level to medical actions
- **Secondary:** Reduce healing time by 1 step per level
- **Wager:** Spend AP for +2 dice (1 AP = +2 dice, no risk)

#### **New Core Skill: Crafting** (Yellow - Time Wager)
*Action Type: Precision*
- **Function:** Creating, repairing, maintaining equipment
- **Level Benefits:** +1 die per level to crafting/repair actions
- **Secondary:** Reduce crafting time by 1 step per level
- **Wager:** Spend AP for +2 dice (1 AP = +2 dice, no risk)

#### **New Core Skill: Discipline** (Blue - Will Wager)
*Action Type: Focus*
- **Function:** Mental fortitude, concentration, willpower
- **Level Benefits:** +1 die per level to mental resistance, focus
- **Secondary:** +1 Will per level
- **Wager:** Spend Will for +1 die each (risk loss on 1s)

### Husbandry Integration Strategy

**Option A: Convert to Technique**
- **Animal Handler** Technique under Influence
- Specialized social approach for animal interaction
- Cost: 2 SP, Prerequisite: Influence Level 1

**Option B: Distribute Functions**
- **Riding:** Mobility skill applications
- **Animal Training:** Influence skill applications
- **Animal Care:** Physik skill applications

**Recommendation:** Option A - maintains thematic coherence

### Survival Integration Strategy

**Current v4 Coverage:**
- **Wilderness Navigation:** Observe skill
- **Endurance/Exposure:** Fortitude skill
- **Movement in Terrain:** Mobility skill
- **Foraging/Tracking:** New Techniques under existing skills

**New Techniques to Add:**
- **Wilderness Ghost** (Observe) - Advanced tracking, stealth in nature
- **Forager** (Observe) - Finding food, water, shelter materials
- **Weather Sense** (Observe) - Predicting weather, reading natural signs
- **Endurance Training** (Fortitude) - Surviving harsh conditions

### Magic Specialization Framework

**Elemental Specialist Techniques:**
- **Fyr Adept** (Evoke) - Fire magic specialization
- **Eorthe Shaper** (Weave) - Earth magic control
- **[Wyrd] Master** pattern for each of 15 Wyrds

**Magical Approach Techniques:**
- **Ritual Caster** (Evoke) - Extended casting for greater effects
- **Battle Mage** (Weave) - Combat magic integration
- **Subtle Caster** (Scry) - Concealed magical effects

---

## Final v4 Skills Framework

### Complete Core Skills List (20 Total)

**Physique Skills (Green - Stamina Wager):**
1. **Fortitude** - Endurance, resisting physical effects *(+1 Stamina per level)*
2. **Heavy Melee** - Heavy weapons and shields, forceful combat *(+1 Force per level)*
3. **Might** - Raw physical power, lifting, endurance *(+1 Carry per level)*
4. **Brawling** - Unarmed combat, grappling *(+1 unarmed damage per level)*

**Finesse Skills (Green - Stamina Wager):**
5. **Mobility** - Movement, dodging, acrobatics *(+1 Speed per level)*
6. **Light Melee** - Light weapons and shields, finesse combat

**Precision Skills (Yellow - Time Wager):**
7. **Stealth** - Moving unseen, hiding
8. **Subtlety** - Lockpicking, traps, sleight of hand
9. **Ranged** - Bows, crossbows, thrown weapons *(+1 Range per level)*
10. **Weave** - Shaping magical effects *(requires Wyrd Lore)*
11. **Observe** - Searching, tracking, noticing details
12. **Physik** - Medicine, healing, anatomy *(reduces healing time)*
13. **Crafting** - Creating, repairing equipment *(reduces crafting time)*

**Focus Skills (Blue - Will Wager):**
14. **Resolve** - Mental fortitude, resisting mental effects *(+1 Will per level)*
15. **Evoke** - Gathering magical power *(requires Wyrd Lore)*
16. **Discipline** - Mental training, concentration *(+1 Will per level)*

**Insight Skills (Blue - Will Wager):**
17. **Reason** - Logic, investigation, problem-solving *(+1 Lore Slot per level)*
18. **Influence** - Persuasion, intimidation, leadership *(+1 Standing per level)*
19. **Scry** - Magical perception, divination *(requires Wyrd Lore)*
20. **Tactics** - Battlefield strategy, command *(+1 initiative per level)*

### Character Archetype Coverage Verification

**✅ All Essential Archetypes Now Covered:**
- **Unarmed Fighter:** Brawling + Techniques
- **Healer:** Physik + Techniques
- **Crafter:** Crafting + Techniques
- **Battlefield Commander:** Tactics + Techniques
- **Animal Handler:** Animal Handler Technique (Influence)
- **Focused Warrior:** Discipline + Techniques
- **Scout:** Observe + Wilderness Techniques
- **All Previous Archetypes:** Maintained from current v4

### New Essential Techniques

#### **Combat Techniques**

**Grappler** (Brawling)
*Prerequisite: Brawling Level 1*
- **Submission Hold:** Spend 1 Stamina to maintain grapple without AP cost
- **Throw:** Spend 2 Stamina to throw grappled target 1 hex per Physique
- **Joint Lock:** Grappled targets suffer -2 dice to all actions

**Field Medic** (Physik)
*Prerequisite: Physik Level 1*
- **Combat Healing:** Treat wounds during combat without penalty
- **Stabilize:** Spend 1 AP to prevent bleeding/dying for one scene
- **Anatomy Knowledge:** +2 dice when targeting specific body locations

**Master Craftsman** (Crafting)
*Prerequisite: Crafting Level 2*
- **Field Repair:** Repair equipment damage in minutes instead of hours
- **Improvised Tools:** Create temporary tools from available materials
- **Quality Work:** Spend extra time to improve equipment stats temporarily

**Battle Commander** (Tactics)
*Prerequisite: Tactics Level 1*
- **Tactical Coordination:** Grant allies +1 die through battlefield commands
- **Initiative Mastery:** Spend 1 Will to act first in any round
- **Formation Fighting:** Adjacent allies gain +1 die to defensive actions

**Mental Fortress** (Discipline)
*Prerequisite: Discipline Level 1*
- **Unshakeable:** Spend 1 Will to ignore fear, intimidation, or mental effects
- **Perfect Focus:** Ignore all Difficulty penalties from distractions
- **Will Recovery:** Recover 1 Will when achieving personal goals

#### **Animal Handler** (Influence)
*Prerequisite: Influence Level 1*
- **Animal Empathy:** Communicate basic concepts with any animal
- **Mount Mastery:** Mounted combat suffers no penalties
- **Beast Companion:** Train animal companion with specific abilities

### Wilderness Techniques

**Wilderness Ghost** (Observe)
*Prerequisite: Observe Level 1*
- **Nature's Embrace:** +3 dice to Stealth in natural environments
- **Trackless Passage:** Leave no trail when moving carefully
- **Animal Speech:** Spend 1 Will to communicate with natural animals
- **Weather Sense:** Predict weather accurately up to 24 hours ahead

**Forager** (Observe)
*Prerequisite: Observe Level 1*
- **Resource Sense:** Automatically find basic survival resources in wilderness
- **Plant Knowledge:** Identify edible, medicinal, and poisonous plants
- **Water Finding:** Locate clean water sources within 1 mile

**Endurance Training** (Fortitude)
*Prerequisite: Fortitude Level 1*
- **Environmental Adaptation:** Ignore minor environmental penalties
- **Extended Exertion:** Spend 1 Stamina to ignore fatigue for one scene
- **Survival Instinct:** +2 dice to resist exposure, hunger, thirst

### Magic Specialization Techniques

**Elemental Adept [Wyrd]** (Evoke)
*Prerequisite: Evoke Level 1, specific Wyrd Lore*
- **Elemental Affinity:** +2 dice when evoking chosen Wyrd
- **Elemental Resistance:** Reduce damage from chosen element by 2
- **Elemental Weapon:** Temporarily imbue weapons with elemental properties

**Ritual Caster** (Evoke)
*Prerequisite: Evoke Level 2*
- **Extended Casting:** Spend exploration time to reduce Will costs
- **Group Rituals:** Combine Will pools with willing participants
- **Permanent Effects:** Create lasting magical changes (high Will cost)

**Battle Mage** (Weave)
*Prerequisite: Weave Level 1*
- **Combat Casting:** No penalties for magic use in combat
- **Spell Sword:** Combine weapon attacks with magical effects
- **Defensive Weaving:** Use magic to enhance armor or create barriers

**Subtle Caster** (Scry)
*Prerequisite: Scry Level 1*
- **Concealed Magic:** Magical effects are undetectable unless specifically sought
- **Silent Casting:** No verbal or obvious physical components required
- **Misdirection:** Observers believe magic originates from different source

---

## Implementation Roadmap

### Phase 1: Critical Core Skills Addition (IMMEDIATE - Publication Blocker)

**Priority 1 - Essential Skills:**
1. **Add Brawling** as 16th Core Skill (Green/Physique)
2. **Add Tactics** as 17th Core Skill (Blue/Insight)
3. **Add Physik** as 18th Core Skill (Yellow/Precision)
4. **Add Crafting** as 19th Core Skill (Yellow/Precision)
5. **Add Discipline** as 20th Core Skill (Blue/Focus)

**Priority 2 - Essential Techniques:**
6. **Add Grappler** Technique (Brawling)
7. **Add Field Medic** Technique (Physik)
8. **Add Battle Commander** Technique (Tactics)
9. **Add Animal Handler** Technique (Influence)

### Phase 2: Wilderness & Magic Integration (SHORT-TERM)

**Wilderness Coverage:**
1. **Add Wilderness Ghost** Technique (Observe)
2. **Add Forager** Technique (Observe)
3. **Add Endurance Training** Technique (Fortitude)

**Magic Specializations:**
4. **Add Elemental Adept** Technique template
5. **Add Ritual Caster** Technique (Evoke)
6. **Add Battle Mage** Technique (Weave)
7. **Add Subtle Caster** Technique (Scry)

### Phase 3: System Polish (MEDIUM-TERM)

**Balance Refinement:**
1. Review existing technique power levels
2. Standardize all technique costs to 2 SP
3. Ensure consistent prerequisite structure
4. Create advancement path examples

**Content Expansion:**
5. Add missing mastery options
6. Create detailed technique descriptions
7. Develop equipment synergy guidelines

### Phase 4: Integration Testing (LONG-TERM)

**Validation:**
1. Create character archetype examples
2. Test character creation with 7 SP budget
3. Verify all essential concepts are covered
4. Playtest skill interactions

---

## Success Criteria

### Publication Readiness Achieved When:
✅ **All Essential Archetypes Covered:** Unarmed fighters, healers, crafters, commanders, animal handlers
✅ **Character Creation Functional:** 7 SP budget allows meaningful starting builds
✅ **System Consistency:** All skills follow v4 design principles
✅ **Complete Coverage:** No gaps in character concept support

### Quality Metrics:
- **Archetype Coverage:** 100% of essential character concepts supported
- **Starting Builds:** 20+ viable character concepts with 7 SP
- **Mechanical Consistency:** All skills follow color-coded wager system
- **Balance:** No skill significantly over/underpowered compared to others

---

## Conclusion

The expanded 20-skill framework resolves all critical gaps while maintaining v4's design philosophy. This approach:

- ✅ **Preserves v4 Innovation:** Maintains mechanical innovation over dice stacking
- ✅ **Completes Coverage:** Supports all essential character archetypes
- ✅ **Maintains Balance:** Follows consistent cost and power structures
- ✅ **Enables Publication:** Removes the critical character creation blocker

**Recommendation:** Implement Phase 1 immediately to resolve the publication blocker, then proceed with subsequent phases for full system completion.

*This framework provides the definitive solution for SellSword v4 skills system completion.*
