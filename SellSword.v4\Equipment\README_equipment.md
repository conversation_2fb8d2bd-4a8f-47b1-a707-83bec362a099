# Equipment

*Domain-specific guide for SellSword v4 weapons, armor, and gear*

*In SellSword, your gear defines your capabilities. A well-equipped sellsword with quality armor and weapons has a decisive advantage over someone relying on natural talent alone.*

## Table of Contents

1. [[#Equipment Philosophy]]
2. [[#Universal Equipment Dice System]]
3. [Weapon Tables](#weapon-tables)
4. [Armor Tables](#armor-tables)
5. [Shield Tables](#shield-tables)
6. [Ammunition & Utility](#ammunition--utility)
7. [Equipment Conditions](#equipment-conditions)
8. [Light Sources](#light-sources)
9. [Two-Handed Use](#two-handed-use)
10. [Domain Context](#domain-context)
11. [Cross-References](#cross-references)

---

## Equipment Philosophy

### The Right Tool in the Right Hands

In SellSword, **equipment defines capability**, but **skill unlocks potential**. A sellsword's survival depends on having the proper gear for each situation, but equipment effectiveness scales dramatically with character skill and specialized training.

**Core Principles:**
- **Gear is important and diverse:** Different tools excel in specific applications
- **Effective in its niche:** Specialized equipment provides significant advantages when used properly
- **Skill matters:** Training dramatically improves equipment effectiveness
- **Universal accessibility:** Anyone can use any gear at basic level
- **Meaningful progression:** Character advancement unlocks equipment potential

### Gear Matters

The difference between success and failure often comes down to preparation and expertise. A parry dagger in the hands of a novice is just another blade, but in the hands of a master duelist, it becomes a formidable defensive tool. A torch can mean the difference between navigating safely and stumbling into danger. The right armor can turn a killing blow into a minor wound.

---

## Universal Gear Dice System

### Core Concept

**All equipment provides 2 dice** to relevant actions, regardless of type or quality. Equipment effectiveness is differentiated through **skill-gated success thresholds** rather than dice quantity.
### Success Thresholds

**Basic Use (No skill required):**
- **Success:** 6 only
- **Effective Dice:** ~0.33 successes average from 2d6
- **Availability:** Anyone can pick up any tool and achieve minimal effectiveness

**Skilled Use (Relevant skill 1+ required):**
- **Success:** 5-6
- **Effective Dice:** ~0.67 successes average from 2d6 (doubles effectiveness)
- **Requirement:** Must have at least 1 level in the relevant skill
- **Examples:** Light Melee 1+ for enhanced parrying, Subtlety 1+ for enhanced lockpicking

**Mastery Use (Specific mastery/technique required):**
- **Success:** 4-6  
- **Effective Dice:** ~1.0 successes average from 2d6 (triples effectiveness)
- **Requirement:** Must have specific mastery or advanced technique
- **Examples:** "Dueling Master", "Lockpicking Master"
### Specialization

**Effective Actions:** Success thresholds only apply to specific actions in which gear is *effective*
- Physique attacks (Body)
- Finesse attacks (Body/Voids)
- Precision attacks (Anywhere)
- Block
- Parry 
- Versatile (Skilled at all, Master at none)
### Stats

- **Carry:** Bulk and weight. For handheld gear (0-3) interacts with speed and force, for worn gear (0-5) interacts with fatigue and encumbrance
- **Durability:** Resistance to damage and wear (0-5)

**Weapon Stats:**
- **Reach:** Effective melee distance added to character's base reach (0-3)
- **Range:** For ranged weapons, optimal distance multiplier (0-5)
- **Cover:** Adds Difficulty dice to attackers targeting the shield bearer (0-3)

**Armor Stats:**
- **Body DR:** Damage reduction for torso and limb hits (0-3 scale)
- **Voids DR:** Damage reduction for gaps, joints, and weak points (0-3 scale)
- **Encumbrance:** VoidsDR - 1, imposes -1 die penalty per point on Finesse, and Precision actions; also reduces Speed
- **Fatigue:** Carry - 1, reduces maximum Stamina
### Action Point Cost (Speed)

- **AP Cost = Carry value**
- **Two-Handed Use:** Reduces AP cost by 1 (minimum 1 AP) for all weapon actions and reach by 1
### Durability Loss

**Standard:** Whenever your gear dice roll (2) 1's it loses 1 point of durability, 2 if attacking and the opponent is armored. Consistent ~2.8% chance on use of any item to degrade during normal use

- Blocking: If the incoming damage is greater than the current gear durability, the item loses the difference
- Armor: When rolling wound dice, any dice with results higher than your current durability count as a loss
- Special: Some opponents have special sunder interactions that directly target durability
### Equipment Traits

- **Slashing:** Gear Successes add Damage
- **Piercing:** Gear Successes add Penetration
- **Crushing:** Gear Successes add Force
- **Blunt:** Doesn't do damage
- **Flexible:** Ignores target cover
---

## Weapon Tables

### Light Melee Weapons

| Name           | Carry | Dur | Effective               | Notes                |
| -------------- | ----- | --- | ----------------------- | -------------------- |
| Throwing Knife | 0     | 3   | Precision, Range 2      |                      |
| Quillon Dagger | 0     | 3   | Finesse, Precision      |                      |
| Rondel Dagger  | 0     | 3   | Physique                | Piercing             |
| Parry Dagger   | 0     | 4   | Parry                   | Blunt                |
| Punch Dagger   | 0     | 3   | Physique, Finesse       | Piercing             |
| Stiletto       | 0     | 2   | Precision               | Piercing             |
| Arming Sword   | 1     | 3   | Versatile               | Slashing             |
| Machete        | 1     | 4   | Physique                | Slashing             |
| Shadow Blade   | 1     | 2   | Finesse, Precision      | Slashing             |
| Hand Axe       | 1     | 3   | Physique, Hook, Range 3 | Slashing             |
| Rapier         | 1     | 3   | Finesse, Parry          | Piercing, Reach      |
| Javelin        | 1     | 2   | Physique, Range 4       | Piercing             |
| Flanged Mace   | 1     | 5   | Physique, Block         | Crushing             |
| War Hammer     | 1     | 4   | Physique                | Crushing OR Piercing |
| Spear          | 1     | 3   | Finesse, Parry          | Reach 1H             |
### Heavy Melee Weapons

| Name        | Carry | Dur | Specialization    | Notes                         |
| ----------- | ----- | --- | ----------------- | ----------------------------- |
| Sabre       | 2     | 3   | Physique, Parry   | Slashing                      |
| Bearded Axe | 2     | 3   | Physique, Hook    | Slashing                      |
| Longsword   | 2     | 4   | Versatile         | Slashing, Reach 1H            |
| Partisan    | 2     | 3   | Finesse, Parry    | Slashing                      |
| Glaive      | 2     | 3   | Physique, Finesse | Slashing                      |
| Poleaxe     | 3     | 5   | Physique          | Piercing OR Crushing          |
| Great Sword | 3     | 4   | Physique, Finesse | Slashing, Reach               |
| Great Axe   | 3     | 3   | Physique, Hook    | Slashing, Reach               |
| Maul        | 4     | 5   | Physique          | Crushing, Reach               |
| Bill        | 4     | 4   | Physique, Hook    | Piercing, Reach 2             |
| Halberd     | 4     | 4   | Physique, Hook    | Slashing Or Piercing, Reach 2 |
### Ranged Weapons

| Name                 | Carry | Dur | Specialization               | Notes |
| -------------------- | ----- | --- | ---------------------------- | ----- |
| Recurve Bow (Light)  | 1     | 3   | Finesse, Precision, Ranged 4 |       |
| Recurve Bow (Medium) | 2     | 3   | Finesse, Precision, Ranged 6 |       |
| Long Bow (Medium)    | 2     | 4   | Precision, Ranged 6          |       |
| Long Bow (Heavy)     | 3     | 4   | Precision, Ranged 8          |       |

---

## Armor Tables

### Light Armor

| Name          | Carry | Dur | Body DR | Voids DR | Enc | Fat | Notes                                    |
| ------------- | ----- | --- | ------- | -------- | --- | --- | ---------------------------------------- |
| Gambeson      | 0     | 1   | 1       | 1        | 0   | 0   | Basic protection, often worn under armor |
| Chainmail     | 2     | 3   | 2       | 2        | 1   | 1   |                                          |
| Plate Harness | 5     | 5   | 3       | 3        | 2   | 4   | Complete Protection                      |
### Heavy Armor

| Name | Carry | Str | Dur | Body DR | Voids DR | Enc | Fat | Notes |
|------|-------|-----|-----|---------|----------|-----|-----|-------|
| Full Plate Harness | 5 | 3 | 5 | 3 | 3 | 2 | 4 | Maximum protection with heavy penalties |

---

## Shield Tables

### Small Shields

| Name | Carry | Str | Dur | Cov | Specialization | Notes |
|------|-------|-----|-----|-----|----------------|-------|
| Steel Buckler | 1 | 1 | 3 | 1 | Block, Parry | Fast, excellent for parrying |

### Medium Shields

| Name | Carry | Str | Dur | Cov | Specialization | Notes |
|------|-------|-----|-----|-----|----------------|-------|
| *[To be populated with v3 data]* |

### Large Shields

| Name | Carry | Str | Dur | Cov | Specialization | Notes |
|------|-------|-----|-----|-----|----------------|-------|
| Steel Round | 3 | 3 | 3 | 3 | Block, Parry | Maximum blocking capability |

---

## Ammunition & Utility

### Ammunition

| Name | Type | Dmg | Prc | Notes |
|------|------|-----|-----|-------|
| Arrow/Bolt - Bodkin | Arrow/Bolt | 1 | 2 | Specialized for defeating armor |

### Utility Gear

| Name | Carry | Dur | Specialization | Function | Notes |
|------|-------|-----|----------------|----------|-------|
| Wooden Torch | 0 | 4 | Light Source | Illumination | Durability = starting dice pool for torch mechanics |
| Lockpicks | 0 | 2 | Lock Work | Subtlety | Enhanced thresholds for lock-related actions |
| Crowbar | 1 | 3 | Prying, Breaking | Might | Enhanced thresholds for leverage actions |

---

## Equipment Conditions

### Sunder

Armor can suffer damage from concentrated Force effects:

**Level 1 (Minor Sunder):** Reduce DR at affected location by 1
- *Repair: 1 AP (tightening straps, clearing debris)*

**Level 2 (Damaged Sunder):** Reduce DR at affected location by 2
- *Repair: 1 Exploration AP plus appropriate supplies*

**Level 3 (Broken Sunder):** Armor provides 0 DR at affected location
- *Repair: Requires downtime and workshop facilities*

**Monster Simplification:** Monsters suffer a flat -1 DR penalty per Force applied (no tracking required). Players track Sunder levels per armor location.

---

## Light Sources

### Torch Mechanics

Torches use a degradation system to represent burning out over time:

**Starting Dice Pool:** Torches begin with 4 dice

**Degradation Check:** Periodically (every 10-15 minutes of game time), roll the torch's current dice pool:
- **No 1s rolled:** Torch continues burning normally
- **Any 1s rolled:** Remove 1 die from the torch's pool for each 1 rolled
- **0 dice remaining:** Torch burns out completely

**Light Quality:** The number of dice remaining determines illumination quality and range.

**Darkness Penalties:** Operating without light imposes Major (-4) or Extreme (-6) Difficulty on most actions.

### Light Source Types

| Source | Starting Dice | Burn Time | Range | Notes |
|--------|---------------|-----------|-------|-------|
| Torch | 4 | 30-60 min | Close | Basic illumination |
| Lantern | 6 | 2-4 hours | Close | Requires oil, better quality |
| Candle | 2 | 1-2 hours | Personal | Minimal illumination |

---

## Two-Handed Use

### Mechanical Benefits

**AP Cost Reduction:** Wielding a weapon with both hands reduces its AP cost by 1 (minimum 1 AP) for:
- Attack actions
- Block maneuvers
- Parry maneuvers
- Other weapon-based actions

**Reach Reduction:** Using a weapon with both hands reduces its effective Reach by 1 (minimum 0)

**Trade-offs:**
- **Advantage:** Faster, more controlled weapon use
- **Disadvantage:** Cannot use a shield or second weapon, reduced reach
- **Tactical Choice:** Speed and control vs. additional defensive options and reach

---

## Integration with Masteries

This system seamlessly integrates with weapon masteries and specialized techniques:

**Weapon Masteries:**
- **"Defensive Specialist":** Unlock 4-6 thresholds for all defensive actions
- **"Armor Breaker":** Unlock 4-6 thresholds for Apply Force stunts
- **"Precision Fighter":** Unlock 4-6 thresholds for Vitals targeting

**Tool Masteries:**
- **"Master Craftsman":** Unlock 4-6 thresholds for all crafting tools
- **"Infiltrator":** Unlock 4-6 thresholds for all stealth-related tools
- **"Field Medic":** Unlock 4-6 thresholds for all medical supplies

### Character Progression Benefits

**Early Game:** All equipment works at basic level, encouraging experimentation
**Mid Game:** Skill investment dramatically improves preferred equipment types
**Late Game:** Mastery specialization creates distinct character niches and expertise

This progression ensures that character advancement feels meaningful while maintaining accessibility for new characters.

---

## Weapon Traits

Traits represent specialized design elements that give weapons unique tactical advantages beyond their basic attributes. These traits reflect a weapon's historical use, construction, and tactical niche.

### Core Weapon Traits

**Crushing** (Maces, Hammers, Mauls)
- When using Apply Force stunt, can choose to make target Compromised instead of pushing back or sundering armor
- No additional Fortitude Test allowed to resist this effect

**Flexible** (Flails, Whips, Chain weapons)
- Attacks ignore Shield Cover bonus
- Can attack around corners or obstacles at +2 Difficulty

**Precise** (Rapiers, Estocs)
- When making Vitals attacks using Precision, ignore the Carry penalty
- Other weapons suffer a penalty equal to their Carry when making Vitals attacks with Precision

**Momentum** (Poleaxes, Greatswords)
- After a successful attack that moves or knocks down an opponent, gain Upper Hand (+2 dice) on your next attack this round
- This does not stack with other sources of Upper Hand

**Quick-Draw** (Throwing knives, Hidden blades)
- Can be drawn and used in the same action without additional AP cost
- Normal weapons require 1 AP to draw unless they have Carry 0

**Disruptive** (Spiked weapons, Barbed designs)
- When you successfully Parry an attack, the attacker becomes Compromised until the end of their next turn
- Only applies when using the weapon defensively

### Trait Assignment

Most weapons have 0-1 traits, with specialized or masterwork weapons potentially having 2. Traits should reflect the weapon's historical use and design rather than simply adding power.

---

## Domain Context

### Working in Equipment Domain

**Before Making Changes:**
1. **Read the Style Guide:** [SellSword_v4_Style_Guide.md](../SellSword_v4_Style_Guide.md)
2. **Check Current Priorities:** [SellSword_v4_TODO.md](../SellSword_v4_TODO.md)
3. **Review Integration Points:** How changes affect Combat, Skills, and Character Creation
4. **Consider Balance:** Equipment power level vs. cost and availability

### Key Formatting Standards

**Equipment Stat Blocks:**
```
| Name | Carry | Str | Dur | Dmg | Prc | Frc | Specialization | Notes |
|------|-------|-----|-----|-----|-----|-----|----------------|-------|
| [Item] | [#] | [#] | [#] | [#] | [#] | [#] | [Actions] | [Description] |
```

**Equipment Descriptions:**
```markdown
#### [Equipment Name]
*Type: [Weapon/Armor/Tool/etc.] | Cost: [Silver pieces] | Availability: [Common/Rare/etc.]*

[Tactical context and typical use]

**Special Properties:** [Unique mechanics or applications]
**Integration:** [How this works with skills and other equipment]
```

### Terminology Standards

**Equipment Terms:**
- **Carry:** AP cost to use (reduced by 1 if two-handed, minimum 1)
- **Strength:** Minimum STR required to use effectively
- **Durability:** Equipment's resistance to damage (universal 2d6 double-1s rule)
- **Specialization:** Which actions benefit from enhanced success thresholds
- **Damage:** Base damage on successful hit
- **Pierce:** Reduces target's effective DR
- **Force:** Impact momentum for special effects
- **Success Thresholds:** 6-only (basic), 5-6 (skilled), 4-6 (mastery)

### Integration Points

**Combat System:**
- **Action Types:** Equipment modifies Physique/Finesse/Precision actions
- **AP Costs:** Equipment Carry values determine action costs
- **Dice Pools:** Equipment adds 2 dice to base actions
- **Success Thresholds:** Skill levels determine equipment effectiveness
- **Damage Resolution:** Pierce and Force interact with armor and wounds

**Skills System:**
- **Weapon Skills:** Unlock enhanced success thresholds for equipment
- **Crafting Skills:** Create and maintain equipment
- **Combat Techniques:** Modify how equipment works
- **Masteries:** Provide specialized equipment effectiveness

**Character Creation:**
- **Starting Equipment:** Based on character concept and budget
- **Carry Capacity:** STR determines maximum equipment load
- **Advancement:** Skill progression unlocks equipment potential

### Balance Considerations

**Cost vs. Benefit:** Better equipment should cost significantly more
**Availability:** Masterwork items should be rare and expensive
**Maintenance:** Quality equipment requires care and resources
**Situational Use:** No single item should be optimal for all situations
**Skill Gating:** Enhanced effectiveness requires appropriate training

---

## Cross-References

### Related Systems
- **Combat Mechanics:** See [Combat README](../Combat/README.md) for attack resolution and damage
- **Skills:** See [Skills README](../Skills/README.md) for weapon skill interactions
- **Character Creation:** See [Characters README](../Characters/README.md) for Carry capacity and Strength requirements
- **Core Rules:** See [SellSword_v4_Core_Rules.md](../SellSword_v4_Core_Rules.md) for basic mechanics

### Equipment Integration
- **Carry Capacity:** Strength and Might determine maximum equipment load
- **Fatigue Effects:** Heavy armor reduces maximum Stamina
- **Encumbrance Penalties:** Restrictive armor affects action dice pools and Speed
- **Maintenance:** Durability determines repair needs and equipment longevity
- **Skill Progression:** Character advancement unlocks equipment effectiveness

---

*Equipment system redesigned with universal 2-dice mechanics and skill-gated effectiveness. For individual equipment details and expanded descriptions, see the Equipment directory files.*

**Version:** 4.1 - Universal Equipment Dice System
**Last Updated:** December 15, 2024

---
