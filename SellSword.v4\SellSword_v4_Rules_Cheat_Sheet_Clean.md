# SellSword v4 — Quick Reference (Clean)

Compact reference for table use. Canonical baselines: Actions = 3 + attribute pair, AP = 4, Speed/Carry formulas confirmed, <PERSON>y vs Force split.

<div style="column-count: 3; column-gap: 10px; font-size: 9pt;">

## Dice & Pools
- Roll d6s. Each 6 = 1 Success.
- Pool = Action + Skill + Equipment + Wager − Difficulty
- Difficulty: Minor −2, Major −4, Extreme −6
- Complexity: Requires multiple successes; partial = failure

## Actions (3 + pair)
- Physique = 3 + STR + AGI
- Finesse = 3 + AGI + DEX
- Precision = 3 + DEX + ACU
- Focus = 3 + ACU + PRE
- Insight = 3 + PRE + INT

## Wagers
- Green (Stamina): 1 Stamina = +1 die; if any 1s on Stamina-wagered dice → lose that Stamina
- Yellow (Time): 1 AP = +2 dice; cap +4 dice per action from Time
- Blue (Will): 1 Will = +1 die; if any 1s on Will-wagered dice → lose that Will
- Magic mishaps check Will dice only

## AP Economy (4 AP/round)
- Move: 1 AP (up to Speed)
- Attack: Weapon Carry AP
- Defend: Dodge 1 AP; Parry/Block = Weapon Carry
- Ready/Interact: Typically Carry or 1 AP (small)
- Take Time (Yellow): 1 AP = +2 dice (max +4 from Time)

## Modifiers
- Upper Hand: +2 dice
- Compromised: −2 dice
- At most one instance of each unless a rule says otherwise

## Hit Locations
- Body: +0 (all actions)
- Voids: +3 (Finesse/Precision focus)
- Vitals: +3 & +1 Complexity (Precision-only)
- Vitals Extra Penalty: −Carry dice unless Precise

## Damage Process (Players)
1) Hit → apply weapon Damage
2) Subtract DR by location
3) If Damage > DR → roll wound dice = penetrating damage
4) Apply wound table (Body/Voids/Vitals)

## Damage vs Opponents (GM)
- Damage − (DR − Pierce) → opponents take wounds equal to penetrating damage (fast resolution)

## Equipment (Core)
- Weapons: Damage, Pierce, Reach, Range, Carry, Durability
- Carry: AP handling; used by some rules (e.g., Vitals penalty)
- Force (separate stat): Mass/impact used for stunts, Sunder thresholds, “applied force” calls
- Armor: Body DR, Voids DR; Encumbrance = VoidsDR − 1; Fatigue = Carry − 1
- Shields: Block (defend dice), Cover (attacker Difficulty), Carry

## Derived Stats
- Speed = 4 + AGI + Mobility + Size − Encumbrance
- Carry = 4 + STR + Might + Size
- Stamina = Physique + Fortitude − Armor Fatigue
- Will = Focus + Resolve
- Lore Slots = 3 + INT + Reason
- Standing Slots = 3 + PRE + Influence

## Magic
- Skills: Evoke (Focus), Weave (Precision), Scry (Insight)
- Flow: Declare → GM sets reqs → Roll → Resolve
- Mishaps: 1s on wagered Will dice; severity per context
- Recovery: Victory (1–2), standout (1), full safety (full Will)

## Basic Stunts (1 extra success each; examples)
- Power Strike: +1 Damage
- Second Wind: Regain 1 Stamina
- Reposition: Move up to AGI (respecting engagement)
- Off-Balance: Target becomes Compromised (−2)
- Target Weakness: −1 target DR (this attack)
- Shake It Off: Remove a minor condition

</div>

**Version:** 4.0  
**Last Updated:** August 4, 2025
