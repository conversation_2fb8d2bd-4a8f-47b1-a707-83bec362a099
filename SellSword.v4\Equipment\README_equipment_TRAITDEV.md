# Equipment System – Trait-Driven Development Draft

*Experimental sandbox for streamlined, trait-based equipment rules in SellSword v4*

---

## 1. Philosophy

In SellSword, **equipment defines capability**, but **skill unlocks potential**. A sellsword's survival depends on having the proper gear for each situation, but equipment effectiveness scales dramatically with character skill and specialized training.

**Core Principles:**
- **Gear is important and diverse:** Different tools excel in specific applications
- **Effective in its niche:** Specialized equipment provides significant advantages when used properly
- **Skill matters:** Training dramatically improves equipment effectiveness
- **Universal accessibility:** Anyone can use any gear at basic level
- **Meaningful progression:** Character advancement unlocks equipment potential

---

## 2. Universal Gear Dice System

### Core Concept

**All equipment provides 2 dice** to relevant actions, regardless of type or quality. Equipment effectiveness is differentiated through **skill-gated success thresholds** rather than dice quantity.

### Success Thresholds

**Basic Use (No skill required):**
- **Success:** 6 only
- **Effective Dice:** ~0.33 successes average from 2d6
- **Availability:** Anyone can pick up any tool and achieve minimal effectiveness

**Skilled Use (Relevant skill required):**
- **Success:** 5-6
- **Effective Dice:** ~0.67 successes average from 2d6 (doubles effectiveness)
- **Requirement:** Must have at least 1 level in the relevant skill

**Mastery Use (Specific mastery/technique required):**
- **Success:** 4-6  
- **Effective Dice:** ~1.0 successes average from 2d6 (triples effectiveness)
- **Requirement:** Must have specific mastery or advanced technique

### Effective Actions

Success thresholds only apply to specific actions in which gear is **Effective**

Weapons and Armor:
- Physique
- Finesse
- Precision
- Block
- Parry
- Hook

Tools:
If a tool makes sense to use in a situation than it is **Effective**. The rule still applies that the user must have at least +1 to the skill being used.
- Using a rope while climbing, Level 1 Mobility
- Picking a lock with a lockpick, Level 1 Subtlety
- Breaking into a door with a crowbar, Level 1 Might
## 3. Values

### Intrinsic Values
These values are inherent to the item itself and range from (0-5)
1. Carry - Bulk and weight of an item. How many gear slots it takes when packed. Determines speed and force for weapons. Determines fatigue for armor.
2. Durability - "Health" of an item and its resistance to breaking during normal use.
3. Damage Reduction (Body/Voids) - How much armor reduces incoming damage. Determines encumbrance (Voids).
4. Reach - Additional melee reach in hexes when item is used
5. Range - **Precision** multiplier in hexes for **Effective** range when thrown or shot
6. Cover - TD Bonus granted when worn or held

### Derived Values
These values are based on the gears intrinsic values and how the character interacts with them.
1. Speed - How many AP's are required to use the item. Carry - 1, minimum of 1. 
2. Force - Additional weapon XS available on attacks. Carry.
3. Fatigue - Lowers maximum stamina available to the player. Carry - 1.
4. Encumbrance - Dice penalty for Physique and Finesse actions. Voids DR - 1.
## 4. Durability

Whenever your gear dice roll (2) 1's it has a chance of losing durability depending on its use. Consistent ~2.8% chance on use

- Tools/Supplies: Unless otherwise specified the gear used breaks or otherwise lost.
- Attacking: Durability loss equal to the DR of the target
- Blocking: Durability loss equal to the Difference of the incoming damage and current Durability
- Armor: Durability loss equal to any wound dice rolled greater than current Durability
## 5. Traits
These represent the specific capabilities of items and differentiate weapons and armor.
### Universal Traits
 - Fragile - Durability loss on a (1,2) roll in addition to (1,1)
 - Stalwart - Typically ignore durability unless specified
 - Exotic - Cant be repaired without specialized equipment
### Weapon Traits
These traits apply when weapons are used **Effectively**

**Slashing** (Swords, Axes)
- Gear Successes raise wound tier
- 1 XS to raise wound tier

**Piercing** (Daggers, Spears, Arrows)
- Gear Successes add penetration
- 1 XS to add penetration

**Crushing** (Maces, Hammers, Mauls)
- Gear Successes sunder armor
- 1 XS on to sunder armor

**Blunt** (Parry daggers, saps)
- Gear Successes remove AP's, Fortitude Resist
- 1 XS on to 

**Flexible** (Flails, Whips, Chain weapons)
- Attacks ignore Cover

**Disruptive** (Whips, Bills, Sword Breakers)
 - 1 XS to 

**Momentum** (Poleaxes, Greatswords)
- 1 XS to lower your next weapon attack by 1 AP

**Forceful** (Bows, )

**Quick-Draw** (Throwing knives, Hidden blades)
- Can be drawn and used in the same action without additional AP cost

**Versatile** (Spears, Quarterstaffs, Longsword)
- Can be used one or two-handed, modifying force and speed

*Most weapons have 1-2 traits. Traits should reflect the weapon's historical use and design rather than simply adding power.*

---
## 5. Weapon Tables

### Light Melee (0-1)

| Name           | Carry | Dur | Effective                       | Traits                                 |
| -------------- | ----- | --- | ------------------------------- | -------------------------------------- |
| Throwing Knife | 0     | 3   | Finesse, Precision              | Ranged 2                               |
| Quillon Dagger | 0     | 3   | Finesse, Parry                  | Slashing                               |
| Rondel Dagger  | 0     | 4   | Physique                        | Piercing                               |
| Parry Dagger   | 0     | 5   | Parry, Hook                     | Blunt                                  |
| Punch Dagger   | 0     | 3   | Physique, Finesse               | Piercing OR Slashing, Exotic           |
| Stiletto       | 0     | 2   | Precision                       | Piercing                               |
| Arming Sword   | 1     | 3   | Physique, Finesse, Parry, Block | Slashing OR Piercing                   |
| Machete        | 1     | 4   | Physique, Block                 | Slashing                               |
| Shadow Blade   | 1     | 2   | Finesse, Parry                  | Slashing AND Piercing, Fragile, Exotic |
| Hand Axe       | 1     | 3   | Physique, Block                 | Slashing, Ranged 3                     |
| Rapier         | 1     | 3   | Finesse, Parry                  | Piercing, Minor Reach                  |
| Javelin        | 1     | 2   | Physique, Finesse               | Piercing, Ranged 4                     |
| Flanged Mace   | 1     | 5   | Physique, Block                 | Crushing                               |
| War Hammer     | 1     | 4   | Physique                        | Crushing OR Piercing                   |
| Spear          | 1     | 3   | Physique, Finesse, Parry        | Piercing OR Slashing, Versatile        |
### Heavy Melee (2-3)

| Name        | Carry | Dur | Effective                       | Notes                             |
| ----------- | ----- | --- | ------------------------------- | --------------------------------- |
| Sabre       | 2     | 3   | Physique, Parry                 | Slashing                          |
| Bearded Axe | 2     | 3   | Physique, Hook                  | Slashing, Minor Reach, Versatile  |
| Longsword   | 2     | 4   | Physique, Finesse, Parry, Block | Slashing, Minor Reach, Versatile  |
| Partisan    | 2     | 3   | Finesse, Parry                  | Slashing, Reach                   |
| Glaive      | 2     | 3   | Physique, Finesse, Parry        | Slashing                          |
| Poleaxe     | 3     | 5   | Physique, Block                 | Piercing OR Crushing OR Slashing  |
| Great Sword | 3     | 4   | Physique, Finesse               | Slashing, Reach, Momentum         |
| Great Axe   | 3     | 3   | Physique, Hook                  | Slashing, Reach                   |
| Maul        | 4     | 5   | Physique                        | Crushing, Minor Reach             |
| Bill        | 4     | 4   | Physique, Hook                  | Piercing, Major Reach             |
| Halberd     | 4     | 4   | Physique, Hook                  | Slashing Or Piercing, Major Reach |
### Ranged

| Name                 | Carry | Dur | Specialization               | Notes |
| -------------------- | ----- | --- | ---------------------------- | ----- |
| Recurve Bow (Light)  | 1     | 3   | Finesse, Precision, Ranged 4 |       |
| Recurve Bow (Medium) | 2     | 3   | Finesse, Precision, Ranged 6 |       |
| Long Bow (Medium)    | 2     | 4   | Precision, Ranged 6          |       |
| Long Bow (Heavy)     | 3     | 4   | Precision, Ranged 8          |       |

---

## Armor Tables

### Light Armor

| Name          | Carry | Dur | Body DR | Voids DR | Enc | Fat | Notes                                    |
| ------------- | ----- | --- | ------- | -------- | --- | --- | ---------------------------------------- |
| Gambeson      | 0     | 1   | 1       | 1        | 0   | 0   | Basic protection, often worn under armor |
| Chainmail     | 2     | 3   | 2       | 2        | 1   | 1   |                                          |
| Plate Harness | 5     | 5   | 3       | 3        | 2   | 4   | Complete Protection                      |
### Heavy Armor

| Name               | Carry | Str | Dur | Body DR | Voids DR | Enc | Fat | Notes                                   |
| ------------------ | ----- | --- | --- | ------- | -------- | --- | --- | --------------------------------------- |
| Full Plate Harness | 5     | 3   | 5   | 3       | 3        | 2   | 4   | Maximum protection with heavy penalties |

---

## Shield Tables

### Small Shields

| Name          | Carry | Str | Dur | Cov | Specialization | Notes                        |
| ------------- | ----- | --- | --- | --- | -------------- | ---------------------------- |
| Steel Buckler | 1     | 1   | 3   | 1   | Block, Parry   | Fast, excellent for parrying |

### Medium Shields

| Name                             | Carry | Str | Dur | Cov | Specialization | Notes |
| -------------------------------- | ----- | --- | --- | --- | -------------- | ----- |
| *[To be populated with v3 data]* |       |     |     |     |                |       |

### Large Shields

| Name        | Carry | Str | Dur | Cov | Specialization | Notes                       |
| ----------- | ----- | --- | --- | --- | -------------- | --------------------------- |
| Steel Round | 3     | 3   | 3   | 3   | Block, Parry   | Maximum blocking capability |

---

## Ammunition & Utility

### Ammunition

| Name                | Type       | Dmg | Prc | Notes                           |
| ------------------- | ---------- | --- | --- | ------------------------------- |
| Arrow/Bolt - Bodkin | Arrow/Bolt | 1   | 2   | Specialized for defeating armor |

### Utility Gear

| Name         | Carry | Dur | Specialization   | Function     | Notes                                               |
| ------------ | ----- | --- | ---------------- | ------------ | --------------------------------------------------- |
| Wooden Torch | 0     | 4   | Light Source     | Illumination | Durability = starting dice pool for torch mechanics |
| Lockpicks    | 0     | 2   | Lock Work        | Subtlety     | Enhanced thresholds for lock-related actions        |
| Crowbar      | 1     | 3   | Prying, Breaking | Might        | Enhanced thresholds for leverage actions            |
