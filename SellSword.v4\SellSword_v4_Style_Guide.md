# SellSword v4 - Style Guide & Documentation Standards

*Comprehensive guidelines for maintaining consistency across all SellSword v4 documentation*

## Table of Contents

1. [Overview](#overview)
2. [Document Structure](#document-structure)
3. [Formatting Standards](#formatting-standards)
4. [Terminology Guidelines](#terminology-guidelines)
5. [Cross-Reference System](#cross-reference-system)
6. [Domain-Specific Guidelines](#domain-specific-guidelines)
7. [File Organization](#file-organization)
8. [Version Control](#version-control)

---

## Overview

### Purpose

This style guide ensures consistency across all SellSword v4 documentation, from core rules to domain-specific guides. It provides:

- **Standardized formatting** for all document types
- **Consistent terminology** using the SellSword Lexicon
- **Cross-reference templates** for linking related content
- **Domain-specific guidelines** for specialized content areas
- **File organization standards** for maintainable project structure

### Design Philosophy

**Clarity Over Complexity:** Documentation should be immediately useful to both developers and players.

**Consistency Builds Trust:** Standardized formatting and terminology create professional, reliable documentation.

**Accessibility First:** All content should be understandable to new contributors while maintaining depth for experienced users.

**Living Documentation:** Guidelines evolve with the project but maintain backward compatibility.

---

## Document Structure

### Standard Document Template

Every SellSword v4 document follows this structure:

```markdown
# [Domain] - [Specific Topic]

*Brief italicized description of the document's purpose and scope*

## Table of Contents

1. [Section 1](#section-1)
2. [Section 2](#section-2)
...

---

## [First Section]

### [Subsection]

Content here...

---

## Cross-References

### Related Systems
- **[System Name]:** See [Link Text](file.md) for [specific aspect]

### [Domain] Integration
- **[Integration Point]:** [Description of how this connects to other systems]

---

*Brief footer note about the document's scope or related files*

**Version:** 4.0  
**Last Updated:** [Actual Date]
```

### Required Sections

**All Documents Must Include:**
- Title with domain and specific topic
- Italicized purpose statement
- Table of Contents (for documents >100 lines)
- Cross-References section
- Version and Last Updated footer

**Domain-Specific Documents Must Include:**
- Philosophy/Overview section explaining the domain's role
- Core Mechanics section with fundamental rules
- Integration notes showing connections to other systems
- Examples demonstrating key concepts

---

## Formatting Standards

### Headers

```markdown
# Document Title (H1 - Only one per document)
## Major Section (H2 - Primary divisions)
### Subsection (H3 - Secondary divisions)
#### Detail Section (H4 - Specific mechanics)
```

**Header Guidelines:**
- Use sentence case: "Character Creation" not "CHARACTER CREATION"
- Be descriptive: "Combat Flow" not "Flow"
- Maintain parallel structure within sections

### Text Formatting

**Bold** for:
- Game terms on first use: **Action Points (AP)**
- Emphasis: **This is critical**
- Stat names: **Strength**, **Damage Reduction**

**Italics** for:
- Document descriptions: *A guide to equipment mechanics*
- Examples: *Example: Longsword vs. Mail Armor*
- Foreign terms: *Fyr* (Fire), *Eorthe* (Earth)

**Code blocks** for:
- Formulas: `Base Pool = Action + Skill + Equipment - Difficulty`
- Stat blocks: `Offense 2, Damage 2, Pierce 1`
- Step-by-step processes

### Lists and Tables

**Bulleted Lists:**
- Use for non-sequential items
- Maintain parallel structure
- Keep items concise

**Numbered Lists:**
- Use for sequential processes
- Start each item with action verb
- Include expected outcomes

**Tables:**
- Include headers for all columns
- Use consistent abbreviations (Str, Dur, Off, Dmg)
- Add Notes column for special cases
- Align numbers right, text left

### Examples and Clarifications

**Example Format:**
```markdown
**Example:** [Brief scenario description]
- [Step 1]: [Action and result]
- [Step 2]: [Action and result]
- [Outcome]: [Final result]
```

**Clarification Format:**
```markdown
*Note: [Important clarification or exception]*
```

---

## Terminology Guidelines

### Core Principles

**Use SellSword Lexicon:** Reference `SellSword_Lexicon.md` for period-appropriate terms
- Prefer "physik" over "medicine"
- Use "harness" for armor, "arms" for weapons
- Apply "artifice" for craftsmanship

**Maintain Consistency:** Once a term is chosen, use it throughout all documents
- "Action Points" not "AP" in formal descriptions
- "Damage Reduction" not "DR" in explanations
- Abbreviations only in tables and quick references

**Prioritize Clarity:** When period terms might confuse, provide context
- "Physik (the healing arts)" on first use
- "Harness (protective armor)" when introducing

### Standard Game Terms

**Character Statistics:**
- Prime Attributes: Strength, Agility, Dexterity, Acuity, Presence, Intellect
- Actions: Physique, Finesse, Precision, Focus, Insight
- Resources: Stamina, Will, Speed, Carry

**Combat Terms:**
- Action Points (AP), not "action economy"
- Damage Reduction (DR), not "armor value"
- Hit Locations: Body, Voids, Vitals
- Combat States: Upper Hand, Compromised

**Magic Terms:**
- Aether (magical realm), Firmament (physical world), Veil (boundary)
- Wyrds (magical domains), not "schools of magic"
- Evoke, Weave, Scry (the three magic skills)

### Abbreviation Standards

**Acceptable in Tables:**
- Str (Strength), Agi (Agility), Dex (Dexterity)
- Phy (Physique), Fin (Finesse), Pre (Precision)
- Off (Offense), Dmg (Damage), Prc (Pierce), Frc (Force)
- DR (Damage Reduction), AP (Action Points), SP (Skill Points)

**Spell Out in Text:**
- First use: "Action Points (AP)"
- Subsequent formal use: "Action Points"
- Quick reference: "AP"

---

## Cross-Reference System

### Internal Links

**Format:** `[Descriptive Text](filename.md#section-anchor)`

**Examples:**
- `[Core Rules - Combat](SellSword_v4_Core_Rules.md#combat)`
- `[Equipment Tables](Equipment_README.md#weapon-tables)`
- `[Magic System](Magic_README.md#the-wyrd-system)`

### Cross-Reference Sections

Every document includes a Cross-References section with:

```markdown
## Cross-References

### Related Systems
- **[System Name]:** See [Link](file.md) for [specific aspect]
- **[System Name]:** See [Link](file.md) for [specific aspect]

### [Domain] Integration
- **[Integration Point]:** [How this connects to other systems]
- **[Integration Point]:** [How this connects to other systems]
```

### Placeholder Format

For incomplete links: `[Core Rules - Combat](#)`

**TODO Tracking:** All placeholder links should be noted in the main TODO file.

---

## Domain-Specific Guidelines

### Equipment Documentation
- Always include complete stat blocks
- Provide tactical context for equipment choices
- Cross-reference with relevant skills and stunts
- Include maintenance and degradation rules

### Skills Documentation
- Explain mechanical benefits clearly
- Provide examples of skill use
- Show interaction with equipment and other skills
- Include advancement costs and prerequisites

### Magic Documentation
- Maintain consistency with Wyrd naming (Old English)
- Explain Aether/Firmament/Veil relationship
- Provide spell examples with clear mechanics
- Address mishap and fatigue systems

### Combat Documentation
- Use step-by-step examples
- Include timing and action point costs
- Show interaction between offense and defense
- Provide tactical guidance

### Character Creation Documentation
- Provide balanced example builds
- Explain derived statistics clearly
- Include equipment recommendations
- Show advancement pathways

---

## File Organization

### Current Directory Structure

```
SellSword.v4/
├── Core Rules & Creation
│   ├── SellSword_v4_Core_Rules.md
│   ├── Character_README.md (NEW)
│   ├── Combat_README.md (NEW)
│   └── Wounds_README.md (NEW)
├── System Documentation
│   ├── Equipment_README.md
│   ├── Skills_README.md
│   ├── Stunts_README.md
│   └── Magic_README.md
├── Lore & World
│   ├── Lore/ (15 Wyrd files)
│   ├── Monsters/ (Creature files)
│   └── Fyr_Wyrd_Lore.md
├── Reference Materials
│   ├── SellSword_Lexicon.md
│   ├── SellSword_Rules_Cheat_Sheet.md
│   └── SellSword_v4_TODO.md
└── Project Management
    ├── SellSword_v4_Style_Guide.md
    └── PROJECT_SETUP.md (NEW)
```

### Recommended Future Organization

For better organization as the project grows:

```
SellSword.v4/
├── Core/
│   ├── SellSword_v4_Core_Rules.md
│   ├── Character_README.md
│   ├── Combat_README.md
│   └── Wounds_README.md
├── Systems/
│   ├── Equipment_README.md
│   ├── Skills_README.md
│   ├── Stunts_README.md
│   └── Magic_README.md
├── Lore/
│   ├── [Wyrd]_Wyrd_Lore.md (15 files)
│   └── Fyr_Wyrd_Lore.md
├── Monsters/
│   ├── Monsters_README.md
│   └── [Individual monster files]
├── Reference/
│   ├── SellSword_Lexicon.md
│   ├── SellSword_Rules_Cheat_Sheet.md
│   └── SellSword_v4_TODO.md
└── Project/
    ├── SellSword_v4_Style_Guide.md
    └── PROJECT_SETUP.md
```

### File Naming Conventions

**Pattern:** `[Domain]_README.md` for main domain guides
**Pattern:** `[Specific_Topic].md` for detailed content
**Pattern:** `[Name]_v4.md` for version-specific content

**Examples:**
- `Equipment_README.md` (main equipment guide)
- `Fyr_Wyrd_Lore.md` (specific Wyrd documentation)
- `Huldrekall_v4.md` (version-specific monster)

---

## Version Control

### Version Numbers
- **Major Version:** 4.0 (current system version)
- **Document Updates:** Track via "Last Updated" field
- **Breaking Changes:** Note in document and TODO file

### Update Tracking
```markdown
**Version:** 4.0  
**Last Updated:** December 15, 2024
```

### Change Documentation
- Major changes noted in TODO file
- Cross-reference updates tracked
- Terminology changes propagated across all files

---

*This style guide serves as the foundation for all SellSword v4 documentation. Refer to domain-specific README files for additional guidelines.*

**Version:** 4.0  
**Last Updated:** December 15, 2024
