# SellSword v4 — Core Rules (Clean Canon)

*A gritty fantasy tabletop RPG where equipment matters, choices have consequences, and survival demands skill, preparation, and nerve.*

## Table of Contents

1. [Design Philosophy](#design-philosophy)
2. [Core Mechanics](#core-mechanics)
3. [Attributes, Actions, and Resources](#attributes-actions-and-resources)
4. [Action Points and Timing](#action-points-and-timing)
5. [Combat](#combat)
6. [Equipment Fundamentals](#equipment-fundamentals)
7. [Derived Statistics](#derived-statistics)
8. [Magic Overview](#magic-overview)
9. [Exploration Overview](#exploration-overview)
10. [Quick Reference](#quick-reference)
11. [Cross-References](#cross-references)
12. [Glossary](#glossary)

---

## Design Philosophy

You are sellswords—professionals who live and die by steel, wits, and prudent expenditure of scarce resources.

Guiding principles:
- Equipment defines capability. The right harness, arms, and tools are decisive.
- Resource pressure drives tension. Stamina, Will, light, and supplies matter.
- Lethality and clarity. Combat is brief and consequential; wounds change the story.
- Player choices over raw stats. Positioning, timing, and approach outmuscle raw numbers.

---

## Core Mechanics

### Dice and Successes
- Roll pools of six-sided dice (d6). Each 6 = 1 Success.
- Most tasks require 1 Success; complex tasks may require more.

### Building a Dice Pool
```
Pool = Action + Skill + Equipment + Wager − Difficulty
```
- Action: One of five Action Scores (see below).
- Skill: Relevant skill dice (typically 0–3).
- Equipment: Typically +2 dice when using suitable gear.
- Wager: Optional dice from resources (see Wagers).
- Difficulty: Environment or opposition imposes negative dice.

### Difficulty and Complexity
- Difficulty applies as −dice: Minor −2, Major −4, Extreme −6.
- Complexity requires multiple successes to fully succeed; failure to reach the threshold is a full failure.

### Wagers (Risk-for-Dice)
- Green (Stamina): Spend 1 Stamina = +1 die. If any 1s appear on the wagered Stamina dice, that Stamina is lost.
- Yellow (Time): Spend 1 Action Point (AP) = +2 dice. Hard cap: +4 dice per action from Time.
- Blue (Will): Spend 1 Will = +1 die. If any 1s appear on the wagered Will dice, that Will is lost. Magic mishap checks use Will dice only (see Magic).

Note: Only the dice spent via a given resource can cause that resource loss on 1s (e.g., 1s on normal pool dice do not cause loss of Stamina/Will).

---

## Attributes, Actions, and Resources

### Prime Attributes (−3 to +3; 0 average)
- Strength (STR), Agility (AGI), Dexterity (DEX), Acuity (ACU), Presence (PRE), Intellect (INT).
- Distribute to a total of +3 (negatives allowed).

### Actions (Base Pool Stats)
Actions define your baseline for task resolution:
- Physique = 3 + STR + AGI (forceful, dynamic)
- Finesse = 3 + AGI + DEX (fast, graceful)
- Precision = 3 + DEX + ACU (careful, accurate)
- Focus = 3 + ACU + PRE (sustained attention, will)
- Insight = 3 + PRE + INT (understanding, deduction)

### Core Resources
- Stamina: Physical exertion. Recovers between encounters if not bleeding.
- Will: Mental resolve and sorcerous exertion. Recover via victories, standout moments, or complete safety.
- Light/Supplies: Exploration-limited resources (see Exploration).

---

## Action Points and Timing

### AP Economy
- Each character has 4 Action Points (AP) per round.
- Common AP costs:
  - Move: 1 AP (up to Speed; GM adjudicates micro-position)
  - Attack: Weapon Carry AP
  - Defend: Dodge 1 AP; Parry/Block = Weapon Carry AP
  - Ready/Interact: Typically Weapon Carry AP or 1 AP for light/small objects
  - Take Time (Yellow Wager): 1 AP provides +2 dice (max +4 from Time per action)

### Resolution Flow (Combat)
1) Declare intents (GM/NPCs then players).
2) Resolve actions in a logical order; simultaneous conflicts resolved by AP costs or GM ruling.
3) Apply Modifiers: Upper Hand (+2 dice) and Compromised (−2 dice).
   - At most one instance of each at a time unless a rule says otherwise.

---

## Combat

### Targeting and Hit Locations
When you attack, declare target and hit location:
- Body: +0 Difficulty (torso, limbs). Available to all Actions.
- Voids: +3 Difficulty (gaps, joints). Available to Finesse/Precision-focused actions.
- Vitals: +3 Difficulty and +1 Complexity (head, heart). Available to Precision actions only.
- Additional Vitals Penalty: Suffer extra penalty dice equal to the weapon’s Carry value when targeting Vitals, unless the weapon has the Precise trait. This penalty is applied as −dice before rolling.

Note: Precision-only gating for Vitals emphasizes careful, skilled strikes.

### Making an Attack
1) Build Pool: Action + Skill + Equipment (+2 if relevant) + Wager − Difficulty (including location and situational modifiers).
2) Roll: Each 6 = 1 Success. 1+ Successes = Hit.
3) Spend Excess: Use extra successes for stunts (e.g., extra damage, repositioning, setting up ally advantage).

### Modifiers
- Upper Hand: +2 dice for flanking, height, ambush, help, or strong advantage.
- Compromised: −2 dice for being flanked, off-balance, prone, blinded, or otherwise disadvantaged.
- Only one instance of each applies unless an effect explicitly stacks.

### Damage and Wounds

Opponents (GM-controlled):
1) Base Damage: Use weapon Damage.
2) Pierce: Reduce effective DR by Pierce.
3) Penetrating Damage: Final Damage − (Target DR − Pierce).
4) Apply Result: Opponents take a wound equal to penetrating damage (fast resolution for GM throughput).

Players:
1) On a successful hit, start with weapon Damage.
2) Subtract DR (by location).
3) If Damage > DR: roll wound dice equal to penetrating damage.
4) Apply wound table result (Body/Voids/Vitals tables). Wounds have tangible effects; there are no hit points.

Armor:
- Provides DR by location (Body/Voids).
- May impose Encumbrance (penalties to Actions and Speed) and Fatigue (reduces max Stamina).

Defense:
- Passive: Target Difficulty (TD) often represented by AGI and size; applied as Difficulty.
- Active: Dodge (1 AP; Finesse + Mobility opposed), Parry/Block (Weapon Carry AP; appropriate skill and equipment opposed).

---

## Equipment Fundamentals

Equipment defines capability. Choosing arms, harness, and tools is as strategic as any action.

### Weapon Stats (Core)
- Carry (0–3): Handling/AP cost; also referenced by some rules (e.g., Vitals penalty unless Precise).
- Durability (1–5): Resistance to wear; rolling multiple 1s against hard targets may degrade items.
- Force (0–2): Post-hit amplifier. Never improves to-hit or Complexity; boosts the conversion effects of traits that actually triggered on this attack.

All weapons deal 1 Damage on a hit. Differentiation comes from trait conversions (triggered by gear successes) and XS spends. There is no Pierce stat—Pierce is provided by the Piercing trait. Reach and Ranged are traits, not core stats.

Force (separate from Carry)
- Carry is strictly for AP handling and interacts with encumbrance/Fatigue systems.
- Force is a post-hit amplifier that never improves to-hit or Complexity. It boosts the conversion effects of traits that actually triggered on this attack.
- Force Bonus per triggered trait on a successful hit:
  - Force 0–1: +0 conversion
  - Force 2–3: +1 conversion
  - Force 4–5: +2 conversion
- A “conversion” equals the trait’s post-hit unit (e.g., +1 Pierce for Piercing; +1 Wound Tier for Slashing/Wounding; +1 Sunder progress for Crushing).
- Force Bonus applies only to traits that triggered via ≥1 real gear success on this roll; you may allocate your Force Bonus freely among all traits that triggered this action. It never counts as a success for hitting, Complexity, or durability checks.

Note: In this Clean Core, Force is defined but not yet added to tables. Force columns will be introduced in the Equipment package (future update).

### Armor
- Body DR and Voids DR determine reduction vs location.
- Encumbrance: VoidsDR − 1 (−dice to Physique, Finesse, Precision; −Speed).
- Fatigue: Carry − 1 (reduces max Stamina).
- Durability: Protection against wear and damage.

### Shields
- Cover (1–3): Core, passive trait that directly adds to the bearer’s Target Difficulty (TD). Cover does not depend on skill or gear dice and is separate from Blocking.
- Blocking with shields: If a shield is Effective for Block/Parry, you roll 2 gear dice for that defense with thresholds applying only to those gear dice; any shield traits that trigger on defense auto-convert on gear successes. A shield that is not Effective can still be used to Block, but its gear dice are 6-only.
- Carry: AP cost and contributes to Fatigue.

---

## Derived Statistics

Compute once during character creation; update if gear or skills change.

- Speed = 4 + AGI + Mobility (skill levels) + Size Mod − Encumbrance
- Carry = 4 + STR + Might (skill levels) + Size Mod
- Stamina = Physique + Fortitude (skill levels) − Armor Fatigue
- Will = Focus + Resolve (skill levels)
- Lore Slots = 3 + INT + Reason (skill levels)
- Standing Slots = 3 + PRE + Influence (skill levels)

Size Mod is typically 0 for human-scale PCs.

---

## Magic Overview

### Cosmology
- Firmament: The physical world.
- Aether: Realm of raw magical forces organized into Wyrds.
- Veil: The boundary mortals manipulate to channel Aether into the Firmament.

### Wyrds (15)
- Elemental: Fyr (Fire), Eorthe (Earth), Wynd (Air), Waeter (Water), Leoht (Light)
- Mental: Mod (Mind), Raed (Logic), Swefn (Dreams), Wyrd (Foresight), Gemynd (Echoes)
- Spiritual: Lif (Life), Deaþ (Death), Sawol (Soul), Sceadu (Shadow), Galdor (Raw Magic)

### Magic Skills
- Evoke (Focus-based): Gather Aetheric power from a known Wyrd.
- Weave (Precision-based): Shape gathered power into effects.
- Scry (Insight-based): Magical perception and divination.

### Casting Flow
1) Declare intent and Wyrd(s).
2) GM sets requirements (skills, Difficulty, Complexity).
3) Roll required magic skills.
4) Resolve effect and costs.

### Mishaps
- Trigger: Check 1s on wagered Will dice used in the casting.
- Severity: Scales with total Will dice result and situation; can cause fatigue, Veil tears, or lasting scars.

Will Recovery (Guidance)
- Victory: 1–2 Will for significant achievements.
- Exceptional Performance: 1 Will for standout moments.
- Complete Safety: Full Will after 8+ hours in absolute security.

---

## Exploration Overview

Light and logistics drive risk in dungeons and delves.

### Light Sources (Conceptual)
- Torch: Dice-based burn-down (degrades over time).
- Lantern/Candle: Alternate pools and burn profiles.

Event Pressure (Guidance)
- Use light state as a modifier to a separate encounter/event roll (avoid double variance spirals).
- Dimmer light increases danger and complication likelihood; brighter light reveals opportunities.

Travel and Navigation
- Regions create travel bands: Close (1–2 days), Far (3–7 days), Very Far (weeks).
- Failures increase time, consume supplies, or attract hazards.

---

## Quick Reference

- Successes: 6s only.
- Pool = Action + Skill + Equipment + Wager − Difficulty.
- Actions = 3 + attribute pair.
- AP = 4 per round.
- Difficulty: −2/−4/−6; Complexity = multiple successes.
- Wagers: Green (Stamina +1 die; 1s on wagered dice lose Stamina), Yellow (1 AP = +2 dice; cap +4/action), Blue (Will +1 die; 1s on wagered dice lose Will; mishaps from Will dice).
- Equipment: Roll 2 gear dice when the item is Effective; thresholds apply only to gear dice (Basic 6; Skilled 5–6; Mastery 4–6). Gear successes add to total successes and also auto-convert via traits (e.g., Piercing adds Pierce, Slashing/Wounding raises wound tier, Crushing adds Sunder progress). After hit, apply Force Bonus per triggered trait: Force 0–1 → +0, 2–3 → +1, 4–5 → +2 conversion points; allocate among triggered traits; Force never helps to-hit. Reach and Ranged are traits (Minor/Major).
- Shields: Cover (1–3) passively adds to bearer TD; Blocking follows the Effective gear dice model (no flat dice bonuses).
- Locations: Body +0; Voids +3; Vitals +3 & +1 Complexity; Vitals also −Carry dice unless Precise; Vitals typically Precision-only.
- Armor: Encumbrance = VoidsDR − 1; Fatigue = Carry − 1.
- Derived: Speed = 4 + AGI + Mobility + Size − Encumbrance; Carry = 4 + STR + Might + Size.

---

## Cross-References

- Combat details and examples: Combat Guide (forthcoming).
- Equipment tables and Force statization: Equipment Guide (forthcoming).
- Magic workings and mishap tables: Magic Guide (forthcoming).
- Exploration procedures and event bands: Exploration Guide (forthcoming).
- Wounds system: Wounds Guide (forthcoming).

---

## Glossary

- Action Points (AP): Per-round budget (4) for actions.
- Actions: The five action scores used to build dice pools.
- Carry: Weapon/gear handling stat that sets AP costs and interacts with encumbrance systems.
- Force: Mass/impact stat used for stunts, knockback, Sunder thresholds, and “applied force” calls (separate from Carry).
- Damage Reduction (DR): Armor’s reduction against incoming Damage at a location.
- Encumbrance: VoidsDR − 1; reduces Speed and imposes −dice on Physique/Finesse/Precision.
- Fatigue: Carry − 1; reduces maximum Stamina.
- Upper Hand / Compromised: +2/−2 dice states; at most one of each at a time.
- Wager: Voluntary expenditure of resources (Stamina/Time/Will) to add dice, with attendant risks.
- Wyrds: Aetheric domains of magical influence.

---

*This document is the canonical, clean baseline for SellSword v4’s core system. Subsequent domain documents must adhere to the numeric baselines and terminology herein.*

**Version:** 4.0  
**Last Updated:** August 4, 2025
