---
# METADATA - Replace placeholder values with actual monster data.
name: "Monster Name"
description_short: "A brief, evocative description (1-2 sentences)."
description_long: |
  A longer, player-facing description.
  This section should convey tone, atmosphere, physical appearance, typical behaviors,
  and any relevant lore or setting information that players might observe or know.
  Use vivid language to bring the monster to life.
size: "Large" # Tiny, Small, Medium, Large, Huge, Gargantuan
type: "Beast" # Beast, Humanoid, Fae, Aetherborn, Primordial, Undead, Construct
tags: [monster, beast, pack_hunter] # Descriptive keywords for categorization

# CORE COMBAT STATS - Refer to README_monsters.md for guidelines.
combat_pool: "4d6" # Base dice pool for most actions
action_points: 3    # AP per round (PCs have 4)
speed: "4" # Movement per AP, add special types: "4 (Climb 3, Fly 5)"
wounds: # Tiered wound threshold pools
  minor: 2    # Number of minor wounds monster can take
  major: 1    # Number of major wounds
  grievous: 0 # Number of grievous wounds
  deadly: 0   # Number of deadly wounds

# DEFENSE - Refer to README_monsters.md for guidelines.
target_difficulty: "TD 1" # Base Target Difficulty (adds Difficulty Dice to attackers)
dr: # Damage Reduction
  body: 1  # Body hit location DR
  voids: 1 # Voids hit location DR
# Complex resistances/immunities should be detailed in Monstrous Traits

# WEAKNESSES & VULNERABILITIES
# Action Scores: Physique, Finesse, Precision, Focus, Insight
weaknesses: [Finesse] # Minor hindrances (-2 dice to combat_pool)
vulnerabilities: []   # Significant flaws (-4 dice to combat_pool)

# ACTIONS
# Define all AP-costing actions the monster can perform.
# Refer to README_monsters.md for action structure and damage scale guidelines.
actions:
  - name: "Bite"
    ap_cost: 1 # Default is 1 AP if not specified
    reach: 1   # 0 for self/touch, 1+ for melee range
    damage: 2  # Monster damage scale 1-6 (players use 1-3)
    pierce: 0  # Reduces target's DR before damage calculation
    force: 0   # Triggers knockback/stagger effects
    notes: "Primary attack with sharp teeth."
    extra_success_effects: # Optional: Effects purchasable with extra successes
      - cost: 1 # Number of extra successes required
        effect: "Target suffers bleeding (1 damage per round until treated)."
      - cost: 2
        effect: "Target is grappled and cannot move away."
  - name: "Intimidating Roar"
    ap_cost: 2
    reach: 0
    damage: 0
    pierce: 0
    force: 0
    notes: "Terrifying bellow that affects enemies within 6 hexes."
    extra_success_effects:
      - cost: 1
        effect: "All enemies within 6 hexes must make Resolve check or become Compromised (-2 dice next round)."
      - cost: 2
        effect: "Affected enemies also lose 1 AP next round from terror."

# MONSTROUS TRAITS
# Passive abilities or unique mechanics that don't cost AP.
# Provide full mechanical descriptions for GM reference.
# Consider cognitive load - prefer Upper Hand over dice bonuses, avoid complex tracking.
monstrous_traits:
  - name: "Pack Hunter"
    description: |
      When attacking the same target as an ally within 2 hexes, gains Upper Hand.
      Does not stack with other sources of Upper Hand.
  - name: "Keen Senses"
    description: |
      Cannot be surprised and automatically detects hidden enemies within 8 hexes.
      Ignores penalties from dim light or darkness when making Focus checks.
  - name: "[Effect Name] (Overwhelm Effect)"
    description: |
      When any [Monster] action achieves 3+ successes, [impactful thematic effect].
      All Overwhelm Effects trigger at 3+ successes for consistency.

# BEHAVIOR & OTHER
tactics: |
  Prefers to attack in groups, using pack tactics to surround isolated targets.
  Focuses on the weakest-looking opponent first. Flees if reduced to 1 wound.
loot: |
  Hide (craftable into leather armor), fangs, 1d6 silver coins from previous victims.
environment: |
  Dense forests, rocky hills, and abandoned ruins. Hunts in packs of 3-6.
---

<!--
GM NOTES (Optional - anything below the '---' is not part of the structured data)

- Concept: Pack-hunting predator that relies on numbers and coordination
- Role: Standard enemy, works best in groups of 3-6
- Synergies: Pack Hunter trait encourages group tactics and positioning
- Counterplay: Area attacks, divide and conquer, bright lights to negate stealth
- Challenge: Standard (appropriate for starting characters in groups)
- Dice Pooling: Consider if this creature fights alone or coordinates attacks
- Cognitive Load: Abilities should be easy to track during play
-->
