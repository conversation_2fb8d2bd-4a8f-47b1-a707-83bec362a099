# MoS-Based Gear Integration for SellSword v5
*Dropping 2d6 Gear Dice for Streamlined Resolution*

---

## Executive Summary

This document explores approaches to eliminate the separate 2d6 gear dice roll in favor of integrating gear effects directly into the 2d10 skill roll's Margin of Success (MoS). The goal is to maintain SellSword's gear importance identity while achieving faster, more elegant resolution.

**Recommended Approach:** MoS as Universal Currency with skill/weapon-specific spending options provides the best balance of simplicity, modularity, and meaningful progression.

---

## Current System Analysis

### v5 Current State
- **Attack Resolution:** 2d10 + Action vs TD
- **Gear Resolution:** Separate 2d6 with proficiency-gated thresholds (6 only / 5-6 / 4-6)
- **Trait Activation:** Gear successes convert to trait effects
- **Force System:** Character Might (STR + weapon Force, 0-2) amplifies triggered traits

### Problems to Solve
1. **Extra Roll Friction:** The 2d6 gear roll slows resolution
2. **Disconnect:** Success on attack doesn't guarantee effective gear use
3. **Variance Frustration:** "Hit but gear failed" feels bad
4. **Complexity:** Two separate resolution systems to track

---

## Proposed Approaches

### Approach 1: MoS Threshold Gates
*Simple breakpoints unlock trait effects*

**Mechanics:**
- **Basic Effect:** MoS 1-2 = 1 trait activation
- **Enhanced Effect:** MoS 3-4 = 2 trait activations  
- **Superior Effect:** MoS 5+ = 3 trait activations

**Proficiency Modifies Thresholds:**
- **Untrained (Level 0):** MoS 3/5/7 for 1/2/3 activations
- **Proficient (Level 1-2):** Standard MoS 1-2/3-4/5+ thresholds
- **Mastery (Level 3):** Reduced MoS 1/2/4 thresholds

**Pros:** Simple, preserves skill-gating, easy to remember
**Cons:** Fixed breakpoints may feel arbitrary, doesn't scale smoothly

---

### Approach 2: MoS Conversion Pool
*Spend MoS points like currency on trait effects*

**Mechanics:**
- **Base Damage:** Fixed 2 points vs monsters
- **Trait Conversions:** Spend MoS to activate weapon traits
  - Piercing: 1 MoS = +1 Pierce (reduce DR)
  - Slashing: 1 MoS = raise wound tier by 1
  - Crushing: 1 MoS = +1 Sunder progress
  - Momentum: 2 MoS = reduce next attack AP by 1

**Proficiency Affects Efficiency:**
- **Untrained:** 2 MoS per conversion
- **Proficient:** 1 MoS per conversion  
- **Mastery:** 1 MoS per conversion + 1 free conversion

**Pros:** Flexible spending, scales naturally, rewards high MoS
**Cons:** More complex decision-making, requires trait cost memorization

---

### Approach 3: Tiered MoS Effects
*Different MoS ranges unlock increasingly powerful effects*

**Mechanics:**
- **MoS 1-2 (Glancing):** Base damage only, no traits
- **MoS 3-4 (Solid Hit):** Base damage + 1 trait (Proficiency 1+ required)
- **MoS 5-6 (Devastating):** Base damage + 2 traits (Proficiency 2+ required)
- **MoS 7+ (Masterful):** Base damage + 3 traits (Proficiency 3 required)

**Pros:** Clear progression, feels cinematic, easy to adjudicate
**Cons:** Harsh breakpoints, low MoS attacks feel weak

---

### Approach 4: Hybrid Force System ⭐ **RECOMMENDED**
*Combine character Might with MoS for trait activation*

**Mechanics:**
- **Available Force:** Character Might (STR + weapon Force, 0-2) + MoS
- **Trait Costs:** Each weapon trait has a Force cost
  - Simple traits (Piercing, Slashing): 1 Force
  - Complex traits (Momentum, Disruptive): 2 Force
  - Exotic traits: 3 Force
- **Proficiency Discounts:** Reduce trait costs by proficiency level

**Example:** A character with Might 2 who achieves MoS 3 has 5 Force to spend. With Proficiency 2, they could activate Piercing (1-2=0 cost, free) + Momentum (2-2=0 cost, free) + another Simple trait (1-2=0 cost, free) and still have 2 Force remaining.

**Pros:** 
- Maintains v5 Force identity
- Scales with both character build AND attack success
- Flexible spending creates tactical decisions
- Proficiency feels meaningfully rewarding
- Works with existing weapon Force ratings

**Cons:** Slightly more complex math, requires trait cost reference

---

### Approach 5: Escalating MoS Tiers
*Weapon specialization affects MoS requirements*

**Base System:**
- **MoS 1:** Base damage
- **MoS 3:** +1 trait effect (Proficiency 1+ required)
- **MoS 5:** +2 trait effects (Proficiency 2+ required)  
- **MoS 7:** +3 trait effects (Proficiency 3 required)

**Weapon Specialization:**
- **Precision weapons:** Reduce MoS thresholds by 1
- **Heavy weapons:** +1 damage at each tier
- **Versatile weapons:** Choose precision OR heavy benefit per attack

**Pros:** Differentiates weapon categories, maintains clear progression
**Cons:** Adds weapon category complexity, fixed thresholds

---

## Durability Without 2d6

Since we lose the "double-1s" trigger, durability needs new mechanics:

**Option A: Natural 1s**
- Any natural 1 on the 2d10 attack roll triggers durability check
- Probability: ~19% chance per attack

**Option B: Extreme Success**
- Durability check when MoS exceeds weapon's Carry rating
- High-performance weapons stress themselves

**Option C: Target-Based**
- Durability loss based on target's DR (attacking) or damage taken (blocking)
- Maintains tactical consideration of target selection

---

## Recommendation: MoS as Universal Currency ⭐

The **MoS Currency System** provides the most elegant and modular approach:

### Core Concept
- **MoS = Success Currency:** Your margin of success becomes points to spend
- **Skill/Weapon Riders:** Each skill, weapon type, and technique defines unique spending options
- **Force & Might Preserved:** These remain separate valuable mechanical levers

### Example Spending Options

**Heavy Melee Weapons:**
- **1 MoS:** Convert 1 Force to +1 damage
- **2 MoS:** Activate weapon trait (if proficient)
- **3 MoS:** Overwhelming attack (stun/knockdown effects)

**Light Melee Weapons:**
- **1 MoS:** +1 to next defense roll this round
- **2 MoS:** Activate weapon trait (if proficient)
- **2 MoS:** Reposition target one range band

**Ranged Weapons:**
- **1 MoS:** Ignore minor cover
- **2 MoS:** Activate weapon trait (if proficient)
- **3 MoS:** Pin target (movement penalty)

**Precision Skills:**
- **1 MoS:** Reduce target's next action TD by 2
- **2 MoS:** Called shot effects (bypass armor, etc.)

**Techniques Add Options:**
- **Berserker Technique:** "1 MoS: Gain +1 Stamina"
- **Duelist Technique:** "1 MoS: Next parry gains advantage"
- **Archer Technique:** "2 MoS: Ricochet to adjacent target"

### System Advantages

1. **Modular Design:** Easy to add new spending options via techniques/masteries
2. **Preserves Existing Values:** Force, Might, and proficiency all remain relevant
3. **Tactical Depth:** Players make meaningful choices about MoS allocation
4. **Scalable Complexity:** Basic weapons have simple options, advanced techniques add more
5. **Clear Progression:** New techniques literally give you new ways to spend success
6. **Maintains Identity:** Different weapon types feel mechanically distinct

### Implementation Notes

- **Base Damage:** All successful hits deal base damage (2 vs monsters, wound table vs players)
- **Proficiency Gates:** Most weapon trait activations require proficiency
- **Force Conversion:** Heavy weapons get unique access to Force→damage conversion
- **Technique Unlocks:** Character advancement adds new MoS spending options

This transforms combat from "roll to hit, roll gear" into "roll to hit, choose how to spend your success" while keeping Force, Might, and weapon diversity as core mechanical differentiators.
