# SellSword v2 Skills System

*Historical documentation of the three-tier Category/Style/Specialization system*

**Purpose:** Document v2 skills system for historical reference and analysis

---

## System Overview

### Design Philosophy
- **Three-Tier Progression** - Category → Style → Specialization
- **Mechanical Innovation** - Higher tiers provide unique abilities, not just dice
- **Playstyle Differentiation** - Styles create distinct approaches to actions
- **Deep Specialization** - Specializations provide mastery of specific tools/techniques

### Core Mechanics
- **Tier 1 (Category):** Broad competence, adds dice (+1/+2/+3), costs 2/3/4 SP per level
- **Tier 2 (Style):** Specific approach, unique benefits, level-less, costs 3 SP flat
- **Tier 3 (Specialization):** Niche mastery, significant benefits, level-less, costs 3 SP flat
- **Prerequisites:** Styles require Category L1, Specializations require relevant Style

### Benefit Philosophy
- **Category:** Primary benefit is adding dice to relevant actions
- **Style:** Focus on HOW actions are performed (Stunts, Complexity reduction, resource use)
- **Specialization:** Focus on mastery within niche (Difficulty reduction, advanced uses)

---

## Complete v2 Category Skills List

### I. Combat Skills

#### **1. Athletics (PHY/FIN)**
*Adds dice to climbing, jumping, swimming, balancing, tumbling*

**Styles:**
- **Acrobat** (FIN) - Improved dodging, tumbling reduces fall damage
- **Bounding Leap** (PHY) - Increase jump distance/height, reduce jumping Difficulty
- **Swimmer** (PHY/FRT) - Reduce penalties for actions in water

#### **2. Brawling (PHY/FIN)**
*Adds dice to unarmed attacks, grapples, shoves, trips*

**Styles:**
- **Grappler** (PHY) - Improved Stunts for holding/pinning opponents
- **Striker** (FIN) - Faster unarmed strikes, disorienting Stunts

#### **3. Ranged Combat (PRC)**
*Adds dice to attacks with bows, crossbows, slings, thrown weapons*

**Styles:**
- **Marksman** - Precision, patience, overcoming obstacles
- **Skirmisher** - Mobility, speed, reacting quickly

**Specializations:**
- **Bows** - Mastery of all bow types (-2 Difficulty)
- **Crossbows** - Mastery of crossbows (-1 Difficulty, -1 AP loading)
- **Slings** - Mastery of slings (-2 Difficulty, +1 Force effect)
- **Thrown Weapons** - Mastery of thrown weapons (-1 Difficulty, +2 range)

#### **4. Weapons Craft (PHY/FIN/PRC)**
*Adds dice to melee attacks, parries, blocks using wielded weapons*

**Styles:**
- **Dueling** (FIN) - Access to Parry/Riposte Stunts, improved single opponent defense
- **Two-Handed** (PHY) - Access to Cleave Stunts, improved Force Stunts
- **Shield Bearer** (FRT) - Improved Block Stunts, Shield Bash ability
- **Ambidexterity** (DEX) - Reduce penalties for fighting with two weapons

**Specializations:**
- **Swords (One-Handed)** - Reduce Parry/Riposte costs with swords
- **Daggers** - Complexity reduction for Voids/Vitals attacks
- **Great Weapons** - Enhance Force/Cleave Stunts
- **Polearms** - Enhance reach/trip Stunts, bracing bonuses
- **Staves** - Enhance defensive/trip/disarm Stunts
- **Heavy Shields** - Enhanced Cover bonus or DR
- **Light Shields** - Faster recovery, lower AP costs
- **Shield Wall** - Bonus when adjacent to other Shield Bearers
- **Paired Light Weapons** - Enhanced paired weapon Stunts
- **Sword and Dagger** - Enhanced offense/defense combinations

### II. Exploration & General Skills

#### **5. Husbandry (FIN/INS)**
*Adds dice to riding, training, or calming animals*

**Styles:**
- **Rider** (FIN) - Improved mounted combat control, difficult maneuvers
- **Animal Ken** (INS) - Better gauge animal moods, calming/training

**Specializations:**
- **Cavalry Tactics** - Enhanced mounted combat formations
- **Trick Riding** - Acrobatic feats on horseback
- **Beast Tamer** - Training animals for specific tasks
- **Veterinarian** - Healing animal wounds

#### **6. Subterfuge (FIN/PRC)**
*Adds dice to stealth, lockpicking, traps, disguise, sleight of hand*

**Styles:**
- **Infiltrator** (FIN) - Reduce Complexity/Difficulty for silent movement
- **Saboteur** (PRC) - Reduce Difficulty/Complexity for traps, sabotage
- **Deceiver** (INS) - Reduce Difficulty for disguises, forgery, sleight of hand

**Specializations:**
- **Cat Burglar** - Bypass specific obstacles (windows, rooftops)
- **Vanish** - Quick disappearance after distraction
- **Lockpicking** - Significantly reduce lock picking Difficulty
- **Trapfinding** - Spotting and analyzing traps
- **Impersonation** - Mimic voices and mannerisms
- **Pickpocket** - Reduce Difficulty/Complexity for lifting items

#### **7. Survival (FRT/FCS)**
*Adds dice to tracking, foraging, navigating wilderness, resisting exposure*

**Styles:**
- **Tracker** (FCS) - Reduce Difficulty for tracks, identify creatures
- **Forager** (AWA) - Reduce Difficulty for finding food/water, plant identification
- **Pioneer** (FRT) - Reduce Difficulty for terrain navigation, shelters, elements

**Specializations:**
- **Urban Tracking** - Apply tracking skills in settlements
- **Monster Hunter** - Identify monster signs, predict behavior
- **Herbalist** - Apply Survival/Physik for medicinal plants
- **Trapper** - Craft and set snares/traps for food
- **Cartographer** - Create accurate maps

### III. Knowledge & Social Skills

#### **8. Crafting (PRC/INT)**
*Adds dice to creating or repairing items (weapons, armor, tools)*

**Styles:**
- **Smith** (STR/PRC) - Focus on metalworking (armor, weapons)
- **Artisan** (DEX/INT) - Focus on finer crafts (leather, fletching, jewelry)
- **Alchemist** (INT) - Brew potions, poultices, acids, explosives

**Specializations:**
- **Weaponsmith** - Craft/repair higher quality weapons
- **Armorsmith** - Craft/repair higher quality armor
- **Fletcher** - Craft superior arrows/bolts
- **Leatherworker** - Craft/repair leather armor, pouches
- **Poisons** - Craft various toxins
- **Elixirs** - Create beneficial concoctions

#### **9. Influence (RES/INS)**
*Adds dice to persuasion, intimidation, deception, leadership, etiquette*

**Styles:**
- **Orator** (PRE) - Inspire crowds, improved persuasion/inspiration Stunts
- **Interrogator** (RES) - Break down resistance, improved intimidation/coercion
- **Manipulator** (INS) - Improved deception Stunts, reading motives

**Specializations:**
- **Diplomacy** - Apply Influence in formal social situations
- **Incite** - Stir crowds to action or panic
- **Intimidate** - Reduce Difficulty for overt threats
- **Subtle Threat** - Imply consequences without direct threats
- **Fast Talk** - Quick lies and bluffing
- **Seduction** - Use charm or allure

#### **10. Physik (PRC/INT)**
*Adds dice to diagnosing ailments, treating wounds, knowledge of anatomy*

**Styles:**
- **Surgeon** (PRC) - Reduce Complexity/Difficulty for serious wounds, surgery
- **Diagnostician** (INT) - Reduce Difficulty for identifying diseases, poisons

**Specializations:**
- **Field Dressing** - Faster/more effective wound stabilization in combat
- **Anatomy** - Apply Physik to identify weak points (Target Weakness synergy)
- **Toxicology** - Identify specific poisons and antidotes
- **Pathology** - Understand diseases and progression

#### **11. Tactics (FCS/INS)**
*Adds dice to assessing battlefields, coordinating allies, predicting enemy actions*

**Styles:**
- **Commander** (PRE) - Grant bonus dice or Stunts to allies via commands
- **Skirmish Leader** (FCS) - Coordinate small unit movements, ambushes
- **Strategist** (INT) - Analyze enemy capabilities, predict actions, plan campaigns

**Specializations:**
- **Formation Fighting** - Coordinate specific unit maneuvers
- **Battlefield Logistics** - Assess supply lines and fortifications
- **Ambush** - Reduce Difficulty for setting up ambushes
- **Counter-Tactic** - Identify and counter enemy maneuvers
- **Siegecraft** - Understand assaulting/defending fortifications

### IV. Magic Skills (Wyrd Manipulation)

#### **12. Evoke (RES)**
*Adds dice to gathering raw Aether associated with known Wyrd*

**Styles:**
- **Channeler** (CON) - Gather Aether more quickly or hold more before shaping
- **Raw Power** (RES) - Overcharge Evocation for greater magnitude at higher Mishap risk

**Specializations:**
- **Wyrd Resonance [Wyrd Name]** - Reduce Difficulty for Evoking specific Wyrd
- **Destructive Surge** - Enhance damaging effects of Evoked power

#### **13. Weave (FCS)**
*Adds dice to shaping, controlling, and maintaining gathered Aether*

**Styles:**
- **Shaper** (FCS) - Reduce Complexity for intricate or multi-target effects
- **Subtle Hand** (INS) - Reduce Difficulty for illusions or subtle manipulations

**Specializations:**
- **Wyrd Control [Wyrd Name]** - Reduce Difficulty for Weaving specific Wyrd
- **Sustained Effects** - Reduce Strain/Difficulty for long-duration effects
- **Illusion Craft** - Create more convincing or complex illusions

#### **14. Scribe (PRC)**
*Adds dice to binding Aether into objects, runes, or locations*

**Styles:**
- **Runesmith** (PRC) - Reduce Complexity/Difficulty for potent or complex runes
- **Artificer** (INT) - Imbue items with temporary or triggered magical effects

**Specializations:**
- **Wyrd Binding [Wyrd Name]** - Reduce Difficulty for Scribing specific Wyrd
- **Wards** - Create more durable or potent protective sigils
- **Enchant Weapon/Armor** - Temporarily enhance gear
- **Potion Brewing (Magical)** - Bind magical effects into consumable liquids

#### **15. Scry (INS)**
*Adds dice to perceiving information or energies via the Aether*

**Styles:**
- **Seer** (AWA) - Reduce Difficulty for divination, foresight, reading auras
- **Empath** (PRE) - Reduce Difficulty for sensing emotions, telepathic communication

**Specializations:**
- **Wyrd Sense [Wyrd Name]** - Reduce Difficulty for Scrying specific Wyrd
- **Truth Reading** - Enhanced ability to detect lies or illusions
- **Mind Reading** - Attempt glimpsing surface thoughts (high Difficulty/Complexity)

---

## System Strengths

### Complete Coverage
- **All Archetypes:** Every character concept had clear progression paths
- **Mechanical Innovation:** Styles and Specializations provided unique abilities
- **Flexible Specialization:** Multiple paths within each Category

### Clear Progression
- **Intuitive Tiers:** Category → Style → Specialization made sense
- **Meaningful Choices:** Each tier provided different types of benefits
- **Balanced Costs:** Flat costs for upper tiers prevented incrementalism

---

## System Weaknesses

### Complexity
- **Many Options:** Large number of Styles and Specializations could overwhelm
- **Prerequisite Chains:** Some advancement paths were restrictive
- **Cost Inconsistencies:** Mixed progression models across tiers

### Balance Issues
- **Power Level Variance:** Some Styles/Specializations significantly stronger
- **Prerequisite Confusion:** Unclear advancement requirements in some cases

---

*This v2 system established the foundation for mechanical innovation that influenced all subsequent versions.*
