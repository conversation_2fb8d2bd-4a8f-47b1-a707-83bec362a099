# SellSword v4 - Complete Combat System

*A comprehensive tactical combat guide integrating trait-based equipment mechanics*

## Table of Contents

1. [Combat Overview](#combat-overview)
2. [Combat Sequence](#combat-sequence)
3. [Action Points & Actions](#action-points--actions)
4. [Attack Resolution](#attack-resolution)
5. [Defense Mechanics](#defense-mechanics)
6. [Damage & Wounds](#damage--wounds)
7. [Equipment Traits in Combat](#equipment-traits-in-combat)
8. [Combat Modifiers](#combat-modifiers)
9. [Special Combat Actions](#special-combat-actions)
10. [Environmental Combat](#environmental-combat)
11. [Comprehensive Examples](#comprehensive-examples)
12. [Quick Reference](#quick-reference)

---

## 1. Combat Overview

SellSword combat emphasizes **tactical positioning**, **resource management**, and **equipment mastery**. Every weapon and armor piece has specific traits that create unique tactical opportunities when used by skilled characters.

### Core Principles
- **Equipment defines capability** - Different weapons excel in specific situations
- **Skill unlocks potential** - Training dramatically improves effectiveness
- **Positioning matters** - Flanking, reach, and cover create advantages
- **Resources are precious** - Stamina and Will expenditure turns battles
- **Wounds have consequences** - Damage creates lasting mechanical effects

---

## 2. Combat Sequence

### Round Structure
Each combat round follows this sequence:

1. **Declaration Phase** (No AP cost)
   - GM declares all NPC actions first
   - Players declare actions in any order, encouraged to coordinate
   - All actions are committed before resolution

2. **Resolution Phase**
   - GM resolves actions in logical order:
     - Lowest AP cost actions first
     - Simultaneous opposed actions (attack vs. defense)
     - Movement after attacks
     - Magic last

3. **Recovery Phase**
   - Apply wound effects
   - Check for unconsciousness/death
   - Reset AP for next round

---

## 3. Action Points & Actions

### Action Point Economy
Each character has **3 AP per round**. AP costs are determined by equipment **Carry** values:

| Action | AP Cost | Notes |
|--------|---------|--------|
| **Move** | 1 AP | Move up to Speed in hexes |
| **Attack** | Weapon Carry | Min 1, max 3 AP |
| **Parry/Block** | Weapon Carry | Min 1, max 3 AP |
| **Dodge** | 1 AP | Universal defense |
| **Ready Weapon** | Weapon Carry | Min 0 (Quick-Draw), max 3 AP |
| **Special Actions** | Variable | Grapple, Sunder, etc. |

### Two-Handed Weapon Adjustment
When using a weapon two-handed:
- **Reduce AP cost by 1** (minimum 1)
- **Reduce Reach by 1** (minimum 0)
- **Increase Force by 1** for stunts

---

## 4. Attack Resolution

### Step 1: Declare Attack Parameters
- **Target:** Specific enemy
- **Hit Location:** Body, Voids, or Vitals
- **Action Type:** Physique, Finesse, or Precision

### Step 2: Build Dice Pool
```
Base Pool = Action Score + Skill Level + Equipment Dice (2) + Wager - Difficulty
```

**Equipment Dice:** Always 2d6, effectiveness determined by success thresholds:
- **No skill:** Success on 6 only
- **Skilled (1+ levels):** Success on 5-6
- **Mastery (specific technique):** Success on 4-6

### Step 3: Apply Combat Modifiers

| Modifier | Dice Adjustment | Examples |
|----------|-----------------|----------|
| **Upper Hand** | +2 dice | Flanking, surprise, elevation |
| **Compromised** | -2 dice | Being flanked, prone, restrained |
| **Cover** | -1 to -4 dice | Partial to total cover |
| **Range** | -1 per hex | Beyond optimal weapon range |

### Step 4: Hit Location Difficulty

| Location | Difficulty | Notes |
|----------|------------|--------|
| **Body** | +0 | Torso, limbs - full armor applies |
| **Voids** | +2 | Gaps, joints - Voids DR applies |
| **Vitals** | +4 | Head, heart - bypasses armor |

### Step 5: Resolve Attack
- **1+ successes:** Hit target
- **Extra successes:** Available for stunts
- **0 successes:** Miss, no effect

---

## 5. Defense Mechanics

### Passive Defense (Always Active)
**Target Difficulty (TD) = 0 + AGI + Size Modifier**
- Applied as Difficulty dice to all attacks
- No AP cost
- Enhanced by high Agility

### Active Defense Options

#### Dodge (1 AP)
- **Pool:** Finesse + Mobility + Equipment Dice (2)
- **Opposed Roll:** Your successes vs. attacker's successes
- **Success:** Attack completely negated
- **Failure:** Attack hits normally

#### Parry (Weapon Carry AP)
- **Pool:** Finesse + Light Melee + Equipment Dice (2)
- **Requirements:** Weapon with Parry trait
- **Success:** Attack negated, may counterattack
- **Counterattack:** 1 XS allows immediate riposte at -2 dice

#### Block (Shield Carry AP)
- **Pool:** Physique + Heavy Melee + Equipment Dice (2)
- **Requirements:** Shield ready
- **Success:** Attack negated
- **Protection:** Shield provides Cover bonus to nearby allies

### Defense Stunts
- **Riposte:** 1 XS for immediate counterattack
- **Shield Bash:** 1 XS to push attacker 1 hex
- **Evasive Maneuver:** 1 XS for Upper Hand on next defense

---

## 6. Damage & Wounds

### Damage Calculation Process

#### 1. Base Damage
Use weapon's **Damage** value (1-3 for most weapons)

#### 2. Apply Pierce
Weapon's **Pierce** reduces target's effective DR:
```
Effective DR = Target DR - Weapon Pierce
(Minimum 0)
```

#### 3. Calculate Penetrating Damage
```
Penetrating Damage = Base Damage - Effective DR
```

#### 4. Resolve Wounds
- **Penetrating Damage ≤ 0:** No wound (attack deflected)
- **Penetrating Damage > 0:** Roll wound dice

### Wound System

#### Player Wounds
Roll d6s equal to penetrating damage, consult wound table:

**Body Wounds Table**
| d6 | Location | Effect |
|----|----------|--------|
| 1 | Left Leg | -1 Speed, -1 Finesse |
| 2 | Right Leg | -1 Speed, -1 Finesse |
| 3 | Chest | -1 Physique, -1 Stamina |
| 4 | Shoulder | -1 to weapon attacks |
| 5 | Left Arm | -1 to shield/left-hand actions |
| 6 | Right Arm | -1 to weapon attacks |

**Voids Wounds Table**
| d6 | Location | Effect |
|----|----------|--------|
| 1-2 | Knees/Groin | -2 Speed, prone on 6 |
| 3-4 | Wrists/Armpits | -2 to relevant arm actions |
| 5-6 | Collar/Head | -2 to all actions, possible stun |

**Vitals Wounds Table**
| d6 | Location | Effect |
|----|----------|--------|
| 1-2 | Achilles/Heart | Critical wound, immediate Will test |
| 3-4 | Gut/Throat | Severe bleeding, ongoing damage |
| 5-6 | Eyes/Brain | Permanent injury or death |

#### Monster Wounds
Monsters use **wound pools** instead of tables:
- **Minor:** 1 damage fills pool
- **Major:** 2 damage fills pool
- **Grievous:** 3 damage fills pool
- **Deadly:** 4+ damage fills pool

### Armor Degradation
When armor prevents damage:
- **Roll 2d6:** If both show 1, armor loses 1 Durability
- **Sunder effects:** Some weapons can directly damage armor
- **Repair:** Requires appropriate tools and time

---

## 7. Equipment Traits in Combat

### Weapon Traits

#### Slashing (Swords, Axes)
- **Effect:** 1 XS raises wound tier by 1
- **Examples:** Longsword, Greatsword, Sabre

#### Piercing (Daggers, Spears, Arrows)
- **Effect:** 1 XS adds +1 Pierce
- **Examples:** Rondel Dagger, Spear, Crossbow bolts

#### Crushing (Maces, Hammers)
- **Effect:** 1 XS causes Sunder 1 (reduces target armor DR by 1)
- **Examples:** War Hammer, Maul, Flanged Mace

#### Blunt (Parry daggers, saps)
- **Effect:** 1 XS removes 1 AP from target
- **Examples:** Parry Dagger, Club, Quarterstaff

#### Flexible (Flails, Whips)
- **Effect:** Attacks ignore Cover bonuses
- **Examples:** Flail, Chain weapons

#### Momentum (Poleaxes, Greatswords)
- **Effect:** 1 XS reduces next attack AP cost by 1
- **Examples:** Greatsword, Poleaxe, Halberd

#### Quick-Draw (Throwing knives)
- **Effect:** Can draw and attack in same action (0 AP to ready)
- **Examples:** Throwing Knife, Hidden Blade

#### Versatile (Spears, Longswords)
- **Effect:** Can switch between one/two-handed use as free action
- **Examples:** Spear, Longsword, Quarterstaff

### Armor Traits

#### Light Armor
- **Gambeson:** Basic protection, no penalties
- **Leather:** +1 to Stealth checks
- **Chain Shirt:** Good balance of protection/mobility

#### Medium Armor
- **Chainmail:** DR 2, Encumbrance 1, Fatigue 1
- **Brigandine:** DR 2, Encumbrance 1, better vs. slashing

#### Heavy Armor
- **Plate Harness:** DR 3, Encumbrance 2, Fatigue 4
- **Full Plate:** DR 3, Voids DR 3, maximum protection

### Shield Traits

#### Buckler
- **Block:** 2 dice, Cover 1, Carry 1
- **Parry Bonus:** +1 to parry attempts

#### Round Shield
- **Block:** 3 dice, Cover 2, Carry 2
- **Balanced:** Good protection/mobility ratio

#### Tower Shield
- **Block:** 2 dice, Cover 4, Carry 4
- **Mobile Cover:** Provides cover to adjacent allies

---

## 8. Combat Modifiers

### Positioning Modifiers

#### Flanking
- **+2 dice (Upper Hand)** when attacking from flank
- **Requires:** Ally opposite target
- **Effect:** Target is Compromised (-2 dice) to defense

#### Higher Ground
- **+1 die** when attacking from elevation
- **+1 Reach** for reach weapons
- **Effect:** Defender suffers -1 to Dodge

#### Rear Attack
- **+3 dice (Upper Hand)** when attacking from behind
- **Effect:** Target cannot use shield defense
- **Special:** Automatic surprise if undetected

### Environmental Modifiers

#### Lighting
- **Bright Light:** No modifier
- **Dim Light:** -2 dice (Major Difficulty)
- **Darkness:** -4 dice (Extreme Difficulty)
- **Magical Darkness:** -6 dice, some traits may bypass

#### Terrain
- **Difficult Terrain:** -1 Speed, -1 to Dodge
- **Slippery:** -2 to all Finesse actions
- **Narrow:** -1 to all Physique actions
- **Cover:** -1 to -4 dice based on coverage

#### Weather
- **Rain:** -1 to Ranged attacks
- **Strong Wind:** -2 to Ranged attacks, -1 to thrown weapons
- **Snow:** -1 Speed, -1 to Mobility checks

---

## 9. Special Combat Actions

### Grappling
- **Initiate:** Physique + Might vs. Target's Physique + Might
- **Success:** Target is Grappled (-2 to all actions)
- **Maintain:** 1 AP per round to maintain grapple
- **Options:** 
  - Throw (1 XS): Target prone, 1 hex away
  - Restrain (2 XS): Target immobilized
  - Disarm (1 XS): Force weapon drop

### Sunder Attack
- **Target:** Enemy weapon or armor
- **Cost:** Normal attack AP
- **Resolution:** Crushing weapon vs. target's Durability
- **Success:** Reduce Durability by 1
- **Critical:** Destroy item if Durability reaches 0

### Called Shot
- **Target:** Specific item or body part
- **Difficulty:** +2 to +4 based on size
- **Effect:** Bypass normal hit location rules
- **Examples:** 
  - Shoot bow string (Difficulty +4)
  - Target weapon hand (Difficulty +2)

### Push/Shove
- **Cost:** 1 AP
- **Resolution:** Physique + Might vs. target's Physique + Fortitude
- **Success:** Target pushed 1 hex per XS
- **Effect:** Target may be pushed into hazards

---

## 10. Environmental Combat

### Using the Environment

#### Improvised Weapons
- **Damage:** 1 (most objects)
- **Traits:** Usually Crushing or Blunt
- **Durability:** 1-2 (break easily)
- **Examples:** Chair, rock, bottle

#### Terrain Features
- **Choke Points:** Force enemies to approach single-file
- **High Ground:** +1 die to attacks, +1 Reach
- **Cover:** Trees, walls, furniture provide varying protection
- **Hazards:** Fire, spikes, pits cause additional damage

#### Climbing & Elevation
- **Climbing:** Mobility check, failure = fall
- **Falling Damage:** 1 damage per 10 feet
- **Elevation Advantage:** +1 die to attacks from above

### Weather Effects

#### Rain
- **Visibility:** -2 to Observe checks beyond Close range
- **Ranged Penalty:** -1 to all Ranged attacks
- **Footing:** -1 to Mobility checks

#### Snow
- **Movement:** -1 Speed, difficult terrain
- **Visibility:** -1 to all attacks beyond Close range
- **Cold Damage:** 1 Stamina per hour without protection

#### Wind
- **Strong Wind:** -2 to Ranged attacks
- **Gale Force:** -4 to Ranged, -1 to thrown weapons
- **Debris:** Possible damage from flying objects

---

## 11. Comprehensive Examples

### Example 1: Skilled Duelist vs. Armored Knight

**Setup:**
- **Duelist:** Rapier (Carry 1, Damage 1, Pierce 2, Traits: Piercing, Minor Reach)
- **Knight:** Longsword (Carry 2, Damage 2, Pierce 1, Traits: Slashing, Versatile)
- **Armor:** Chainmail (Body DR 2, Voids DR 2, Encumbrance 1, Fatigue 1)
- **Shield:** Buckler (Block 2, Cover 1, Carry 1)

**Round 1:**
- **Duelist AP:** 3 (Rapier Carry 1)
- **Knight AP:** 2 (Longsword Carry 2, reduced by 1 for two-handed)

**Duelist's Turn:**
1. **Attack:** Targets Voids (+2 Difficulty)
   - Pool: Finesse 4 + Light Melee 2 + Equipment 2 - Difficulty 2 = 6 dice
   - Rolls: 3 successes (1 extra success)
   - **Stunt:** Find the Gap (1 XS) - reduces armor by 1 tier
   - **Damage:** 1 vs. Effective DR 1 (2-2-1= -1, minimum 0)
   - **Wound:** 1d6 roll = 4 (Armpit), -2 to right arm actions

**Knight's Turn:**
1. **Counterattack:** Targets Body (+0 Difficulty)
   - Pool: Physique 3 + Heavy Melee 1 + Equipment 2 = 6 dice
   - Rolls: 2 successes (1 extra success)
   - **Stunt:** Powerful Strike (1 XS) - wound tier +1
   - **Damage:** 2 vs. Effective DR 0 (no armor on duelist)
   - **Wound:** 2d6 roll = 3, 5 (Chest, Left Arm), -1 Physique, -1 weapon attacks

### Example 2: Shield Wall Tactics

**Setup:**
- **3 Guards:** Round shields, spears
- **2 Bandits:** Greatswords, light armor

**Formation:**
- Guards form shield wall (adjacent shields)
- Each guard provides Cover 2 to adjacent allies
- Combined Cover bonus: +4 dice difficulty to attacks

**Bandit Attack:**
- **Greatsword:** Damage 3, Reach 2, Momentum trait
- **Attack:** Against middle guard
- **Difficulty:** +4 from shield wall
- **Pool:** Physique 4 + Heavy Melee 2 + Equipment 2 - 4 = 4 dice
- **Result:** 1 success (no extra successes)
- **Damage:** 3 vs. DR 2 (chainmail) = 1 penetrating
- **Wound:** Minor wound to guard

### Example 3: Environmental Combat

**Setup:**
- **Rogue:** On rooftop with crossbow
- **Guards:** In alley below
- **Environment:** Rain, slippery roof

**Rogue's Attack:**
- **Crossbow:** Damage 2, Pierce 2, Range 6, Precise
- **Target:** Guard's Vitals (+4 Difficulty)
- **Modifiers:** 
  - Upper Hand (+2): Elevation advantage
  - Rain (-1): Weather penalty
  - Precise weapon: Ignores Vitals penalty
- **Pool:** Precision 5 + Ranged 3 + Equipment 2 + 2 - 1 = 11 dice
- **Result:** 4 successes (3 extra successes)
- **Stunts:** 
  - 1 XS: Pinpoint Shot (Upper Hand on next attack)
  - 1 XS: Extra damage (+1)
  - 1 XS: Knock target prone
- **Damage:** 3 vs. DR 0 (Vitals bypass armor) = 3 penetrating
- **Wound:** Critical hit, guard incapacitated

### Example 4: Armor Sundering

**Setup:**
- **Warrior:** War Hammer (Crushing trait)
- **Enemy Knight:** Full plate (DR 3, Voids DR 3)

**Sunder Attack:**
- **Target:** Knight's breastplate
- **Resolution:** Normal attack vs. Body
- **Success:** 2 successes (1 extra success)
- **Stunt:** Sunder (1 XS) - reduces armor DR by 1
- **Result:** Plate armor now DR 2 for rest of combat
- **Follow-up:** Next attacks more likely to penetrate

---

## 12. Quick Reference

### Combat Flow Summary
1. **Declare** target, location, action
2. **Build** pool: Action + Skill + Equipment (2) + Modifiers
3. **Roll** dice, count successes (6s)
4. **Apply** damage, wounds, stunts
5. **Resolve** ongoing effects

### AP Costs Quick Chart
| Weapon Type | AP Cost | Two-Handed |
|-------------|---------|------------|
| Light (0-1) | 1 AP | 1 AP |
| Medium (2) | 2 AP | 1 AP |
| Heavy (3+) | 3 AP | 2 AP |

### Success Thresholds
| Skill Level | Success Range |
|-------------|---------------|
| No Skill | 6 only |
| Skilled (1+) | 5-6 |
| Mastery | 4-6 |

### Common Stunts
| Stunt | Cost | Effect |
|-------|------|--------|
| Extra Damage | 1 XS | +1 wound tier |
| Reposition | 1 XS | Move AGI hexes |
| Sunder | 1 XS | -1 armor DR |
| Disarm | 2 XS | Force weapon drop |
| Knockback | 1 XS | Push target |

### Wound Severity
| Damage | Player Effect | Monster Effect |
|--------|---------------|----------------|
| 1 | Minor wound | Minor pool |
| 2 | Major wound | Major pool |
| 3+ | Grievous wound | Grievous+ pool |

---

*This document provides complete mechanical integration between SellSword's trait-based equipment system and tactical combat. Use alongside equipment tables for full reference.*

**Version:** 4.1 Complete  
**Last Updated:** July 14, 2025
