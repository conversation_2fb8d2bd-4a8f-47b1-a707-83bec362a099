# SellSword v4 — Equipment (Clean Canon)

*A trait-driven equipment system where gear defines capability, skill unlocks thresholds, and Force amplifies post-hit effects.*

## Table of Contents

- [SellSword v4 — Equipment (Clean Canon)](#sellsword-v4--equipment-clean-canon)
  - [Table of Contents](#table-of-contents)
  - [Philosophy](#philosophy)
  - [Core Equipment Stats](#core-equipment-stats)
  - [Effective Gear Dice](#effective-gear-dice)
  - [Post-Hit Conversions](#post-hit-conversions)
  - [Weapons](#weapons)
  - [Shields](#shields)
  - [Armor](#armor)
  - [Durability](#durability)
  - [Reference Tables (Initial Pass)](#reference-tables-initial-pass)
    - [Light Melee (Carry 0–1)](#light-melee-carry-01)
    - [Heavy Melee (Carry 2–3)](#heavy-melee-carry-23)
    - [Ranged](#ranged)
    - [Armor](#armor-1)
    - [Shields](#shields-1)
  - [Trait Glossary](#trait-glossary)
  - [Cross-References](#cross-references)

---

## Philosophy

In SellSword, equipment defines capability; skill unlocks potential. All weapons deal 1 damage on a hit. Differentiation comes from trait conversions triggered by gear successes, with Force providing a neutral post-hit amplifier that boosts whatever trait was actually used.

Design pillars:
- Minimal core stats: Carry, Durability, Force
- Threshold-gated gear dice (Basic/Skilled/Mastery)
- Automatic trait conversion from gear successes
- Force 0/1/2 conversions applied only to triggered traits
- Shields provide passive Cover to TD; blocking uses the gear-dice model

---

## Core Equipment Stats

- Carry (0–3): Handling/AP cost. Referenced by:
  - Attack/Parry/Block AP costs in Combat
  - Vitals penalty (−Carry dice unless Precise)
  - Armor Fatigue (Fatigue = Carry − 1)

- Durability (1–5): Item wear tolerance.

- Force (0–2): Post-hit amplifier for traits that actually triggered on this roll.
  - Force never improves to-hit or Complexity.
  - Force Bonus per triggered trait on a successful hit:
    - Force 0: +0 conversion
    - Force 1: +1 conversion
    - Force 2: +2 conversion
  - A “conversion” equals a trait’s post-hit unit (e.g., +1 Pierce, +1 Wound Tier, +1 Sunder progress).
  - Allocate Force conversions only among traits that triggered (≥1 real gear success); they do not count as successes for to-hit, Complexity, or durability checks.

---

## Effective Gear Dice

All equipment rolls 2 gear dice when it is Effective for the action. If not Effective, the gear dice still roll but use Basic thresholds only.

- Thresholds apply only to the 2 gear dice:
  - Basic (no relevant skill): 6
  - Skilled (relevant skill ≥ 1): 5–6
  - Mastery (specific mastery/technique): 4–6

- Gear successes:
  - Add to total successes (to hit and meet Complexity)
  - Automatically convert via the item’s traits (post-hit effects)

- Excess Successes (XS):
  - After hit, XS from the whole roll can buy additional trait effects where listed
  - XS does not change thresholds; it purchases explicit options

- All weapons deal 1 damage on a hit. Traits and XS modify outcomes post-hit.

---

## Post-Hit Conversions

Sequence after a successful hit:
1) Identify which traits triggered (≥1 gear success each).
2) Apply automatic conversions from gear successes for those traits.
3) Apply Force Bonus (0/1/2 conversions) among triggered traits.
4) Spend XS on additional trait options or generic stunts (e.g., Power Strike).
5) Resolve DR, Pierce, Sunder, and Wound effects per trait outcomes.

Neutral amplifier:
- Force is trait-agnostic and applies equally to melee and ranged if the trait triggered (e.g., Crushing on a warhammer, Wounding/Piercing on arrows).

---

## Weapons

Weapons have only three core stats: Carry, Durability, Force. All other differentiation is traits.

- All weapons deal 1 damage on a hit.
- Reach and Ranged are traits (Minor/Major).
- Pierce is not a stat; Piercing is a trait.
- A weapon can be Effective for: Physique, Finesse, Precision, Block, Parry, Hook (as listed per profile).
- Versatile weapons may be used one- or two-handed; item profile will define Effective and trait behavior in each mode.

---

## Shields

Two distinct functions:
1) Cover (1–3): Passive trait that adds directly to the bearer’s Target Difficulty (TD). This is always on and independent of gear dice or skill.
2) Blocking/Parrying: If the shield is Effective for Block/Parry, you roll 2 gear dice for that defense; thresholds apply only to those gear dice, and any defensive traits auto-convert on gear successes. If a shield is not Effective, it can still be used to Block, but gear dice are 6-only.

- Shields list: Carry, Durability, Cover, Effective, Traits.

---

## Armor

Armor remains DR-based:
- Body DR and Voids DR reduce incoming damage at their locations.
- Encumbrance = VoidsDR − 1: −dice to Physique, Finesse, Precision; reduces Speed.
- Fatigue = Carry − 1: reduces maximum Stamina.
- Durability: protects against wear, may degrade under sustained damage.

---

## Durability

Durability checks occur when your gear dice roll double 1s on a relevant action (≈2.8% per use). On a check:
- Tools/Supplies: Often break or are consumed unless specified.
- Attacking: Durability loss equal to the target’s DR.
- Blocking: Durability loss equal to the difference of incoming damage and current Durability (min 0).
- Armor: Durability loss equal to any wound dice rolled that exceed current Durability.

Traits modify these rules (e.g., Fragile, Stalwart).

---

## Reference Tables (Initial Pass)

Tables reflect the new schema. Force values are proposed for playtesting and should be tuned.

Legend:
- Effective: Which actions gain the 2 gear dice with thresholds (e.g., Physique, Finesse, Precision, Block, Parry, Hook)
- Traits: Trait list (reach/ranged expressed as traits: Minor Reach, Major Reach; Minor Ranged, Major Ranged)

### Light Melee (Carry 0–1)

| Name           | Carry | Dur | Force | Effective                       | Traits                                   |
| -------------- | ----- | --- | ----- | ------------------------------- | ---------------------------------------- |
| Throwing Knife | 0     | 3   | 0     | Finesse, Precision              | Minor Ranged, Quick-Draw                 |
| Quillon Dagger | 0     | 3   | 0     | Finesse, Parry                  | Slashing                                 |
| Rondel Dagger  | 0     | 4   | 0     | Physique                        | Piercing                                 |
| Parry Dagger   | 0     | 5   | 0     | Parry, Hook                     | Blunt                                    |
| Punch Dagger   | 0     | 3   | 0     | Physique, Finesse               | Piercing, Slashing, Exotic               |
| Stiletto       | 0     | 2   | 0     | Precision                       | Piercing                                 |
| Arming Sword   | 1     | 3   | 1     | Physique, Finesse, Parry, Block | Slashing, Piercing, Versatile            |
| Machete        | 1     | 4   | 1     | Physique, Block                 | Slashing                                 |
| Shadow Blade   | 1     | 2   | 0     | Finesse, Parry                  | Slashing, Piercing, Fragile, Exotic      |
| Hand Axe       | 1     | 3   | 1     | Physique, Block                 | Slashing, Minor Ranged                   |
| Rapier         | 1     | 3   | 0     | Finesse, Parry                  | Piercing, Minor Reach                    |
| Javelin        | 1     | 2   | 0     | Physique, Finesse               | Piercing, Major Ranged                   |
| Flanged Mace   | 1     | 5   | 1     | Physique, Block                 | Crushing                                 |
| War Hammer     | 1     | 4   | 1     | Physique                        | Crushing, Piercing                       |
| Spear          | 1     | 3   | 1     | Physique, Finesse, Parry        | Piercing, Slashing, Versatile, Minor Reach |

### Heavy Melee (Carry 2–3)

| Name        | Carry | Dur | Force | Effective                       | Traits                                  |
| ----------- | ----- | --- | ----- | ------------------------------- | --------------------------------------- |
| Sabre       | 2     | 3   | 1     | Physique, Parry                 | Slashing                                |
| Bearded Axe | 2     | 3   | 1     | Physique, Hook                  | Slashing, Minor Reach, Versatile        |
| Longsword   | 2     | 4   | 1     | Physique, Finesse, Parry, Block | Slashing, Minor Reach, Versatile        |
| Partisan    | 2     | 3   | 1     | Finesse, Parry                  | Slashing, Minor Reach                   |
| Glaive      | 2     | 3   | 1     | Physique, Finesse, Parry        | Slashing, Major Reach                   |
| Poleaxe     | 3     | 5   | 2     | Physique, Block                 | Piercing, Crushing, Slashing, Major Reach |
| Greatsword  | 3     | 4   | 2     | Physique, Finesse               | Slashing, Major Reach, Momentum         |
| Great Axe   | 3     | 3   | 2     | Physique, Hook                  | Slashing, Major Reach                   |
| Maul        | 3     | 5   | 2     | Physique                        | Crushing, Minor Reach                   |
| Bill        | 3     | 4   | 2     | Physique, Hook                  | Piercing, Major Reach                   |
| Halberd     | 3     | 4   | 2     | Physique, Hook                  | Slashing, Piercing, Major Reach         |

### Ranged

Ranged traits will tie into the attacker’s Precision (PRC) in a future rules pass. For now, Minor/Major Ranged indicate relative effective bands.

| Name                 | Carry | Dur | Force | Effective               | Traits                     |
| -------------------- | ----- | --- | ----- | ----------------------- | -------------------------- |
| Recurve Bow (Light)  | 1     | 3   | 1     | Finesse, Precision      | Minor Ranged               |
| Recurve Bow (Medium) | 2     | 3   | 1     | Finesse, Precision      | Major Ranged               |
| Longbow (Medium)     | 2     | 4   | 1     | Precision               | Major Ranged               |
| Longbow (Heavy)      | 3     | 4   | 2     | Precision               | Major Ranged               |

### Armor

| Name          | Carry | Dur | Body DR | Voids DR | Enc | Fat | Notes                                    |
| ------------- | ----- | --- | ------- | -------- | --- | --- | ---------------------------------------- |
| Gambeson      | 0     | 1   | 1       | 1        | 0   | 0   | Basic protection, often under armor      |
| Chainmail     | 2     | 3   | 2       | 2        | 1   | 1   |                                          |
| Plate Harness | 5     | 5   | 3       | 3        | 2   | 4   | Complete protection                       |

Enc = VoidsDR − 1; Fat = Carry − 1.

### Shields

| Name          | Carry | Dur | Cover | Effective     | Traits                |
| ------------- | ----- | --- | ----- | ------------- | --------------------- |
| Steel Buckler | 1     | 3   | 1     | Block, Parry  | Quick-Draw (optional) |
| Round Shield  | 2     | 3   | 2     | Block         | Stalwart (optional)   |
| Tower Shield  | 3     | 4   | 3     | Block         | Stalwart (optional)   |

Cover: Passive TD bonus (always on). Blocking uses Effective gear dice (no flat dice bonuses).

---

## Trait Glossary

Core conversion traits
- Piercing (Weapons/Ammo): Each gear success grants +1 Pierce; XS: +1 Pierce per XS. Force adds +0/1/2 extra Pierce conversions if Piercing triggered. Pierce reduces the target’s effective DR before assessing penetration.

- Slashing / Wounding (Weapons/Ammo): Each gear success raises wound tier by +1; XS: +1 tier per XS (table pacing TBD). Force adds +0/1/2 extra tier raises if Wounding triggered.

- Crushing / Sunder (Weapons): Each gear success adds +1 Sunder progress toward degrading armor. XS: +1 progress per XS. Force adds +0/1/2 extra progress if Crushing triggered. Armor thresholds for Sunder are defined by armor class (to be tuned).

Positioning and range traits
- Minor Reach / Major Reach (Weapons): Grants extended engagement distance/control. Minor Reach is a modest extension; Major Reach is substantial. Specific grid/hex effects appear in the Combat/Equipment package.

- Minor Ranged / Major Ranged (Weapons/Ammo): Indicates relative band effectiveness. Final ranged performance will scale with attacker Precision (PRC) in a future rules pass.

Utility/handling traits
- Flexible: Attacks may ignore shield Cover in specific circumstances (detail tuned in Equipment package).

- Disruptive: On hit, apply a momentum/control effect (e.g., −1 AP to target’s next action) per conversion. Exact effect/cost will be tuned.

- Momentum: Reduces your next weapon attack AP by 1 per conversion (timing/frequency limits to be tuned).

- Quick-Draw: Draw and attack with the item in the same action without extra AP.

- Versatile: Can be used one- or two-handed; item profile specifies Effective sets and Force behavior per mode.

Durability traits
- Fragile: Triggers durability loss on (1,2) in addition to (1,1).
- Stalwart: Typically ignore durability loss unless specified (or raise thresholds).
- Exotic: Cannot be repaired without specialized facilities/parts.

Shields
- Cover (1–3): Passive TD bonus; independent of skill/gear dice and separate from Blocking.
- Shield Defensive Traits (optional): If present, they trigger and convert on defensive gear successes when the shield is Effective for Block/Parry.

Notes
- This glossary captures current traits and their conversion currencies. Some trait effects include “to be tuned” notes—these are intentionally left for Package 3 numeric playtesting.

---

## Cross-References

- Core System: See [SellSword_v4_Core_Rules_Clean.md](../SellSword_v4_Core_Rules_Clean.md#equipment-fundamentals) for the universal gear dice model and Force amplifier interaction.
- Combat: Hit locations, AP costs, Vitals penalties, and defense details live in Combat (forthcoming).
- Wounds: Wound tables and tier effects live in Wounds Guide (forthcoming).

**Version:** 4.0  
**Last Updated:** August 4, 2025
