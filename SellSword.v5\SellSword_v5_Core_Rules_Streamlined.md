# SellSword v5 — Core Rules (Streamlined Draft)

A faster, curved-resolution core that keeps Sell<PERSON><PERSON>’s identity: you roll to hit on 2d10, resolve gear on 2d6, and equipment, positioning, and resource pressure still matter. Defense is an action you take and roll for—when you choose to defend, your defense roll opposes the attack.

---

## Table of Contents

- Design Goals
- Core Mechanics
- Attributes, Actions, and Skills
- Character Creation
- Resources, Derived Stats, and Formulas
- Action Economy and Timing
- Checks, Advantage, and Difficulty
- Combat Procedure (Static vs Active Defense)
- Hit Locations
- Gear and Damage (2d6 Effects)
- Magic Overview and Mishaps
- Momentum (Optional, Lean)
- Quick Reference
- Glossary

---

## Design Goals

- Streamlined resolution: one core check on 2d10; gear outcomes on 2d6.
- Keep gear identity: proficiency-gated thresholds (6 / 5–6 / 4–6), traits, DR, Sunder.
- Preserve tactical DNA: hit locations, defense choices, and positioning.
- Bounded math: rely on advantage/disadvantage over stacking modifiers.
- Player agency in defense: if you spend actions to defend, you roll to defend.

---

## Core Mechanics

- Core check: roll 2d10 + Action versus a Target Difficulty (TD).
- Advantage/Disadvantage: roll 3d10, keep best/worst 2. Never stack.
- On hit, players roll gear 2d6 to generate damage and trait conversions; DR applies.
- Resources (Stamina/Will) are simple spenders: grant advantage to checks or fuel techniques. No extra d6 added to the 2d10 check in the baseline.

---

## Attributes, Actions, and Skills

- Attributes (−3 to +3, 0 average): STR, AGI, DEX, AWR (Awareness), PRE (Presence), INT (Intellect)
- Actions (derived from attribute pairs; no modifier cap):
  - Physique (PHY) = STR + AGI
  - Finesse (FIN) = AGI + DEX
  - Precision (PRC) = DEX + AWR
  - Focus (FOC) = PRE + INT
  - Insight (INS) = AWR + PRE

- Core Skills (15; Level 0–3; affect 2d10 via skill level states; do not change gear/magic thresholds):
  - Physique: Fortitude, Heavy Melee, Might
  - Finesse: Mobility, Light Melee, Stealth
  - Precision: Subtlety, Ranged, Weave
  - Focus: Resolve, Observe, Evoke
  - Insight: Reason, Influence, Scry

Major skills do not add flat bonuses to 2d10 checks. They influence your roll via skill level states (advantage/disadvantage/TD shifts) and unlock techniques/masteries. Gear and magic dice thresholds are governed by Proficiencies (see below).


### Linked Skills and Starting Levels

- Attribute-linked baselines: Six skills start with a baseline equal to their linked attribute modifier (including negatives):
  - STR → Might
  - AGI → Mobility
  - DEX → Subtlety
  - AWR → Observe
  - PRE → Resolve
  - INT → Reason

- Skill Levels: Track each major skill at Level 0–3. After character creation, simply refer to the skill’s current Level (don’t recalculate baselines separately).

- What skill levels do (they do not add flat numbers to the 2d10 check):
  - Level 0 (Untrained): The GM may impose Disadvantage on demanding tasks; trivial tasks may not require a roll.
  - Level 1 (Trained): Roll normally. You can ignore one minor situational penalty in this domain at the GM’s discretion.
  - Level 2 (Expert): If you Take Time (declare Slow) or have proper setup/tools, gain Advantage; otherwise roll normally.
  - Level 3 (Master): Advantage by default. If you already have Advantage from another source, reduce TD by 2 instead (Advantage never stacks).

- SP Costs (advancing a major skill):
  - 0 → 1: 1 SP
  - 1 → 2: 2 SP
  - 2 → 3: 3 SP

Notes
- Major skills unlock techniques, masteries, and gear/magic effectiveness via proficiencies. They do not modify the 2d10 roll with flat bonuses; they create advantage contexts and permissions.




### Proficiencies (Weapons & Magic)

Proficiencies govern gear/magic dice thresholds (2d6, or magic dice) and are separate from major skills.

- Weapon Families:
  - Heavy (e.g., greatswords, polearms; tower shields count as Heavy)
  - Light (e.g., arming swords, daggers; bucklers count as Light)
  - Ranged (e.g., bows, crossbows, thrown)

- Magic Proficiencies:
  - Per‑Wyrd (Fyr, Eorthe, Wynd, Waeter, Leoht; Mod, Raed, Swefn, Wyrd, Gemynd; Lif, Deaþ, Sawol, Sceadu, Galdor)

- Proficiency Levels: 0–3 with SP costs 1/2/3
  - Thresholds by Proficiency Level:
    - Level 0 (Untrained): 6 only
    - Level 1 (Proficient): 5–6
    - Level 2 (Proficient): 5–6
    - Level 3 (Mastery): 4–6
  - Prerequisites: A matching major skill at Level ≥ 1 is recommended but not required (campaign option). Proficiencies never affect 2d10 checks—only gear/magic dice.

---

## Character Creation

1) Concept and Background
- Pick an archetype concept and a few defining details about training, gear habits, and stance in the world.

2) Attributes (Point Buy)
- Start at 0 in each of the six attributes.
- Distribute a total of +3 across attributes. Standard array example: 2, 1, 1, 0, 0, −1 (you may assign these as you wish).
- No attribute may start below −1 or above +3 at creation.

3) Actions (Record)
- Compute your five action values from attribute pairs (see above).
- For checks, each Action contribution is capped at +3, but the underlying pair can exceed +3 for non-check uses (e.g., resource formulas).

4) Skills & Proficiencies (7 SP total for now)
- Major Skills (Purchased Level costs): 0→1:1 SP, 1→2:2 SP, 2→3:3 SP
- Proficiencies (Heavy, Light, Ranged; per‑Wyrd) costs: 0→1:1 SP, 1→2:2 SP, 2→3:3 SP
- Assign SP across majors and proficiencies. Majors affect 2d10 via skill level states; proficiencies set gear/magic thresholds.

5) Starting Resources and Derived Stats
- Attribute Budget: total +3 across attributes (see below for standard array)
- Stamina = PHY + Fortitude (Level)
- Will = FOC + Resolve (Level)
- Carry = 4 + STR + Might (Level) + Size Mod

6) Starting Equipment
- Choose a primary weapon, a sidearm or tool, armor, and traveling kit consistent with your concept. Record weapon traits (Piercing/Wounding/Sunder, Parry/Block ratings, Reach/Range, Carry) and armor DR by location, Encumbrance, and Fatigue.

7) Wyrd Knowledge (if using magic)
- Pick known Wyrds and initial magic skills as permitted by your concept/campaign (see Magic Overview).

---

## Resources and Derived Stats

### Color Tags
Skills map to color tags that determine resource interactions:
- Green (Stamina domain): Physical exertion skills - Fortitude, Heavy Melee, Might, Mobility, Light Melee
- Blue (Will domain): Mental/wyrd exertion skills - Resolve, Observe, Evoke, Reason, Influence, Scry
- Yellow (Time domain): Skills that benefit from taking time - Subtlety, Ranged, Weave

### Resources
- Stamina (Green): Spend 1 to gain Advantage on Green‑tagged checks or to fuel specific techniques/taps. Recovers between encounters when safe and not bleeding.
- Will (Blue): Spend 1 to gain Advantage on Blue‑tagged checks or to shape/magnify a cast. Recovers after victories, standout moments, and complete safety (sleep).
- Carry: 4 + STR + Might (Level) + Size Mod

---

## Action Economy: Fast and Slow

- Rounds have a Declarative Phase (GM declares, then players declare, including any Active Defenses and whether their actions are Fast or Slow) and an Active Phase (GM resolves from fastest to slowest by discretion).
- Fast actions resolve before Slow actions.
- Take Time (Yellow only): If a check is in a Yellow domain and you declare it as Slow, gain Advantage on that check.
- Defense speed depends on gear:
  - Bucklers/light blocks are Fast; tower/heavy blocks are Slow.
  - Parry (Precision) is Fast by default unless gear specifies otherwise.
  - Dodge (Finesse) is Fast.
- Movement and range bands:
  - Range bands replace grids/hexes: close (melee ~2 yds), near (reach melee ~5 yds), far (ranged ~10 yds). Adjust per arena.
  - Moving within near: Fast action
  - Moving within far: Slow action
  - You can convert a far move to Fast by either rolling Mobility (2d10 + FIN vs TD set by arena/terrain) OR spending 1 Stamina. Failure on the roll means it remains Slow.

---

## Round Structure: Declarative and Active Phases

Notes on non‑gear checks and skill levels
- When no gear/magic dice are rolled, apply skill level states instead of adding flat numbers:
  - Untrained (Level 0): Disadvantage unless trivial
  - Trained (Level 1): normal
  - Expert (Level 2): if you Take Time or have proper setup/tools, gain Advantage; otherwise normal
  - Mastery (Level 3): Advantage by default; if you already have Advantage, reduce TD by 2 instead
- Tools satisfy “proper setup/tools,” and may negate Untrained Disadvantage at GM discretion.


- Declarative Phase
  - GM declares all opponents’ intents (targets, moves, attacks, notable effects).
  - Players declare their intents. Each player may:
    - Declare actions (move/attack/cast/etc.) as Fast or Slow (if eligible).
    - Declare Active Defenses for this round by specifying defense type (Dodge/Parry/Block), the speed (Fast/Slow, if relevant to gear), and the opponent(s) or coordinated group whose attacks you will oppose.

- Active Phase
  - Resolve movement, attacks, and effects in GM-determined order, typically from fastest to slowest actions.
  - When an attack resolves against a defender who declared an Active Defense against it, resolve an opposed roll (see Combat Procedure). If no defense was declared for that attack, resolve using Static Resolution.

Notes
- This model removes the need for a separate “defensive stance.” Defense is a declared choice with action costs, not a reaction granted for free.

---

## Checks, Advantage, and Difficulty

- Standard check: 2d10 + relevant Action ≥ TD.
- Difficulty ladder: TD 8 (Easy), 11 (Average), 14 (Difficult), 17 (Extreme), 20 (Impossible).
- Advantage/Disadvantage: 3d10 keep best/worst 2. Never stack; take the single strongest state that applies.
- Major skills do not add flat bonuses to the 2d10 roll; they influence the roll via skill level states and unlock contexts for advantage (via techniques/masteries). Gear/magic thresholds come from Proficiencies.

---

## Combat Procedure (Static vs Active Defense)

Two modes exist per attack, determined by the defender’s choice.

- Static Resolution (fast path; defender does not spend an action):
  1) Attacker declares target and hit location (Body/Voids/Vitals).
  2) Attacker rolls 2d10 + Action vs TD:
     - Body: TD 11
     - Voids: TD 14
     - Vitals: TD 14 and Disadvantage on the attack
  3) On success: proceed to Gear and Damage.

- Active Defense (defender declared defense; this is their action):
  1) Attacker declares target and hit location.
  2) If the defender declared an Active Defense against this attack during the Declarative Phase, resolve using the declared defense type and speed:
     - Dodge (FIN + Mobility): Fast; may be declared against a single opponent or a group if they attack together
     - Parry (PRC + Light/Heavy Melee with a parry-capable weapon): Fast by default (unless gear says Slow); melee only
     - Block (PHY + Heavy Melee or Shield): Bucklers/light blocks are Fast; tower/heavy blocks are Slow; may be declared against a single opponent or a coordinated group (melee or ranged)
     - Cover does not interact with Block; it only modifies the attacker’s TD as a passive environmental factor.
     - If no matching defense was declared, use Static Resolution for this attack.
  3) Opposed Roll:
     - Attacker: 2d10 + Action, apply advantage/disadvantage from situation and location:
       - Voids: Attacker suffers Disadvantage
       - Vitals: Attacker suffers Disadvantage; Defender gains Advantage on the defense roll
     - Defender: 2d10 + appropriate Action (FIN/PRC/PHY as above). Apply defense-appropriate advantage from traits (e.g., shields) if any.
  4) Compare totals:
     - If defender total ≥ attacker total: no hit (defense succeeds). On a strong margin (≥3), defender may gain 1 Momentum (optional rule; see Momentum).
     - If attacker total > defender total: hit; Margin of Success (MoS) = difference. Proceed to Gear and Damage.
  5) Multiple attacks: You must have declared sufficient defenses; if you run out of declared coverage, resolve remaining as Static.


Notes
- This preserves single-roll attacks when defenders choose not to spend, and converts to exactly two rolls (attack + defense) when they do. Location penalties translate to advantage states so math stays bounded and consistent.

---

## Hit Locations

- Body: baseline
- Voids: hard-to-hit openings
- Vitals: lethal points

Location effects
- Static: Body TD 11; Voids TD 14; Vitals TD 14 + Disadvantage (attacker)
- Active Defense: Voids → Attacker Disadvantage; Vitals → Attacker Disadvantage and Defender Advantage on defense roll

Precise gear/techniques can reduce Vitals penalties—at most cancel one of these penalties (not both) unless a specific mastery says otherwise.

---

## Gear and Damage (2d6 Effects)

On a successful hit (player vs opponent):

- Roll Gear 2d6:
  - Thresholds (by relevant Proficiency with the gear):
    - Level 0 (Untrained): 6 only count as successes
    - Level 1–2 (Proficient): 5–6
    - Level 3 (Mastery): 4–6
- Damage and Conversions:
  - Base damage vs monsters/creatures: 2d6 (apply after DR)
  - Trait conversions per gear success (examples; see equipment list):
    - Piercing: +1 Pierce per success (reduces DR for this attack)
    - Wounding/Slashing: raise wound tier by +1 per success (players vs player tables)
    - Crushing/Sunder: +1 Sunder progress per success (degrade armor by type)
- Force (0–2):
  - After seeing gear successes, allocate up to Force points across traits that actually triggered (i.e., had ≥1 success). Force doesn’t create new traits; it amplifies triggered ones.
- Durability:
  - Double 1s on the 2d6 gear roll trigger wear; apply weapon/tool durability rules.
- Against player characters:
  - Use wound tables by location (v4 continuity). Attacker’s conversions raise severity; defender armor DR applies by location.
- Against monsters (asymmetric):
  - Use HP and DR. Damage = 2d6 − (DR − Pierce); add Wounding/Sunder as applicable.

---

## Magic Overview and Mishaps

- Wyrds (15): Fyr, Eorthe, Wynd, Waeter, Leoht; Mod, Raed, Swefn, Wyrd, Gemynd; Lif, Deaþ, Sawol, Sceadu, Galdor
- Magic Skills:
  - Evoke (FOC): Gather power
  - Weave (PRC): Shape effect
  - Scry (INS): Magical perception

Casting (baseline)
- Check: 2d10 + Action (usually FOC or PRC) ≥ TD set by the GM. Magic skills affect the roll via skill level states (advantage/disadvantage/TD shifts).
- Will spend: Choose one per cast—gain Advantage on the casting check OR add +1 Force‑equivalent to the effect’s shaping (post‑hit style).
- Mishaps:
  - Safe channel (baseline Will spend): no inherent mishap risk.
  - Push (optional risk): You may roll up to your Resolve level in Will risk dice (d6) to supercharge an effect. Any two 1s among these Will dice cause a mishap; each 6 adds +1 additional effect step per your Wyrd (GM adjudicates). This concentrates mishap risk into explicit “I push it” moments.

---

## Momentum (Optional, Lean)

- Earning: On any successful action roll, if your MoS ≥ +3, gain 1 Momentum Point (MP).
- Spending: At most 1 MP per action. Keep the menu short:
  - PHY: Shove (reposition target), Second Wind (recover 1 Stamina), Off‑Balance (apply Compromised)
  - FIN: Reposition (small free move), Feint (Disadvantage vs your next attack), Setup (ally gains Advantage vs same target)
  - PRC: Exploit Weakness (−2 TD for next ally vs same target), Pinpoint (treat one gear die of 4 as a success), Bypass Minor Cover (treat target as one step less concealed for your next attack)
- Skip Momentum in your first sessions if you want maximum speed; add later once the core feels comfortable.

---

## Quick Reference

- Check: 2d10 + Action ≥ TD (8/11/14/17/20)
- Advantage/Disadvantage: 3d10 keep best/worst 2 (never stack)
- Action Economy: Declare Fast or Slow; Fast resolves before Slow. Yellow “Take Time” = declare Slow to gain Advantage (Yellow only).
- Active Defense (declare in Declarative Phase; scope per opponent or coordinated group):
  - Dodge = Finesse (Fast)
  - Parry = Precision (Fast unless gear says Slow; melee only)
  - Block = Physique (Bucklers/light Fast; tower/heavy Slow)
  - If no defense was declared for an incoming attack, resolve as Static.
- Hit Locations:
  - Static: Body 11; Voids 14; Vitals 14 + Disadvantage (attacker)
  - Active Defense: Voids → attacker Disadvantage; Vitals → attacker Disadvantage + defender Advantage
- On Hit (players): Roll gear 2d6
  - Thresholds: 6 only / 5–6 / 4–6 by Proficiency Level
  - Base damage vs monsters: 2d6; apply DR and Pierce; apply Wounding/Sunder per successes
  - Might: After seeing successes, allocate up to your Might (STR + weapon Force, capped 0–2) to traits that triggered
  - Durability: double‑1s on gear dice → wear
- Resources:
  - Stamina (Green): Spend 1 to gain Advantage on Green‑tagged checks or to convert a far move to Fast
  - Will (Blue): Spend 1 to gain Advantage on Blue‑tagged checks or to shape/magnify a cast
  - Yellow (Time): If the check is Yellow‑tagged, declare it Slow to gain Advantage
- Range Bands & Movement:
  - close (~2 yds), near (~5 yds), far (~10 yds); moving within near is Fast, within far is Slow; convert far→Fast by Mobility roll or 1 Stamina
- Derived:
  - Stamina = PHY + Fortitude (Level)
  - Will = FOC + Resolve (Level)
  - Carry = 4 + STR + Might (Level) + Size Mod

---

## Glossary

- Action (PHY/FIN/PRC/FOC/INS): Derived pairing of attributes; no modifier cap.
- Advantage/Disadvantage: Roll 3d10 keep best/worst 2.
- Carry (weapon stat): Weight/slot measure and heft for attacks/blocks/parries.
- DR (Damage Reduction): Armor mitigation by hit location.
- Force: Weapon stat (0–2) that adds to character Might for post-hit conversion points.
- Might: Character's post-hit conversion budget (STR + weapon Force, capped 0–2) usable only on traits that triggered.
- MoS (Margin of Success): Attacker–defender total difference on opposed checks; or roll–TD on static.
- Momentum: Optional MoS-based currency; capped at 1 spend per action in this core.
- Traits (gear): Keywords that convert gear successes into effects (Piercing/Wounding/Sunder, etc.).

**Version:** 5.0 (Streamlined Draft)
**Last Updated:** August 8, 2025

