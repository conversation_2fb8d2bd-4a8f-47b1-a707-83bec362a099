# Potential Skills Integration

## New Unarmed Combat Techniques

#### Brawler (Technique)
*Prerequisite: Might Level 1*
*Cost: 2 SP*

You fight with raw aggression and dirty tactics, using your strength and willingness to do whatever it takes to win.

- **Stunt Enhancement: Overwhelming Attack:** When attacking without a weapon, Overwhelming Attack costs 1 instead of 2 extra successes.
- **Dirty Fighting:** Wager at least 2 Stamina to ignore the dice penalty for unarmed unconventional attacks (headbutts, eye pokes, groin strikes).

#### Pugilist (Technique)
*Prerequisite: Heavy Melee Level 1
*Cost: 2 SP*

You deliver devastating strikes with trained precision, focusing on powerful blows that can end fights quickly.

- **Stunt Enhancement: Flowing Strike:** When attacking without a weapon, Flowing Strike costs 1 extra success instead of 2 and removes the -2 dice penalty when used on the same target.
- **Striker:** Any hit on an unarmored target converts your Force to additional damage.

#### Martial Artist (Technique)
*Prerequisite: Light Melee Level 1*
*Cost: 2 SP*

You use an opponent's momentum and positioning against them, emphasizing redirection over brute force.

- **Stunt Enhancement: Apply Force:** When attacking without  and may use the opponents attack value if defending.
- **Unarmed Parry:** You may parry melee attacks unarmed at -2 dice, but success grants you the Upper Hand on your next attack against that opponent.

#### Pressure Point Fighter (Technique)
*Prerequisite: Subtlety Level 1*
*Cost: 2 SP*

You strike with surgical precision at vital points, using knowledge of anatomy to disable rather than destroy.

- **Stunt Enhancement: Disrupt:** When attacking unarmed, Disrupt causes the target to lose 2 AP's instead of 1.
- **Nerve Strike:** Spend 2 AP when making an unarmed attack to target pressure points. Success deals no damage but paralyzes one limb for 1 round.

---

## Priority 1: Essential Combat Archetypes

### Unarmed Fighter Support

#### **Iron Fists** (Mastery) 🔍
*Prerequisite: Grappler OR Striker*
*Historical Source: v3 Brawling Specialization*

**Concept:** Enhanced unarmed damage through conditioning and technique

**Historical Mechanics:**
- Unarmed strikes deal more base damage
- Minor armor penetration for unarmed attacks
- Treat fists as weapons for certain purposes

**v4 Adaptation Needed:**
- Define damage enhancement values
- Specify armor penetration mechanics
- Balance against weapon alternatives

### Battlefield Commander Support

#### **Battle Commander** (Technique) 🔍
*Prerequisite: Influence Level 1*
*Historical Source: v2/v3 Tactics Styles*

**Concept:** Tactical coordination and ally support through battlefield commands

**Historical Mechanics:**
- Grant bonus dice to allies via commands
- Coordinate unit movements and formations
- Enhance ally morale and effectiveness

**v4 Adaptation Needed:**
- Define Will costs for commands
- Specify range and targeting for ally benefits
- Integrate with v4 action economy

### Healer/Medic Support

#### **Field Medic** (Technique) 🔍
*Prerequisite: Reason Level 1*
*Historical Source: v2/v3 Physik Styles*

**Concept:** Combat healing and emergency medical treatment

**Historical Mechanics:**
- Faster/more effective wound stabilization
- Reduce Complexity/Difficulty for treating wounds
- Emergency medical procedures

**v4 Adaptation Needed:**
- Define healing mechanics within v4 wound system
- Specify AP costs and Stamina requirements
- Balance healing effectiveness vs. magical alternatives

### Defender/Tank Support

#### **Shield Master** (Technique) 🔍
*Prerequisite: Heavy Melee Level 1*
*Historical Source: v2/v3 Weapons Craft Specializations*

**Concept:** Enhanced defensive capabilities with shields

**Historical Mechanics:**
- Enhanced blocking Stunts
- Spend Stamina to reduce block AP costs
- Shield-based offensive capabilities

**v4 Adaptation Needed:**
- Define Stamina costs for enhanced blocking
- Specify shield-based attack options
- Integrate with v4 defensive mechanics

#### **Armor Specialist** (Technique) 🔍
*Prerequisite: Fortitude Level 1*
*Historical Source: v3/v4 Previous Mastery*

**Concept:** Efficient armor use and maintenance

**Historical Mechanics:**
- Reduce Encumbrance and Fatigue penalties
- Repair armor conditions in field
- Environmental adaptation while armored

**v4 Adaptation Needed:**
- Define encumbrance reduction values
- Specify field repair mechanics
- Balance armor benefits vs. mobility

---

## Priority 2: Essential Utility Archetypes

### Crafter/Artisan Support

#### **Weaponsmith** (Technique) 🔍
*Prerequisite: Subtlety Level 1*
*Historical Source: v2/v3 Crafting Specializations*

**Concept:** Weapon creation, repair, and enhancement

**Historical Mechanics:**
- Craft/repair higher quality weapons
- Apply Crafting to understand weapon flaws
- Enhanced weapon maintenance

**v4 Adaptation Needed:**
- Define crafting time and resource costs
- Specify quality improvements and durability
- Integrate with v4 equipment system

#### **Armorsmith** (Technique) 🔍
*Prerequisite: Subtlety Level 1*
*Historical Source: v2/v3 Crafting Specializations*

**Concept:** Armor creation, repair, and fitting

**Historical Mechanics:**
- Craft/repair higher quality armor
- Apply Crafting to fit armor properly
- Enhanced armor maintenance

**v4 Adaptation Needed:**
- Define armor crafting mechanics
- Specify fitting bonuses and customization
- Balance crafted vs. found equipment

### Scout/Tracker Support

#### **Wilderness Ghost** (Technique) 🔍
*Prerequisite: Stealth Level 1*
*Historical Source: v4 Previous Technique*

**Concept:** Natural stealth and tracking in wilderness environments

**Historical Mechanics:**
- Gain +2 dice to Stealth in wilderness
- Leave no trail when moving carefully
- Animal empathy and natural camouflage

**v4 Adaptation Needed:**
- Define environmental bonuses
- Specify trackless movement mechanics
- Clarify animal interaction rules

#### **Urban Shadow** (Technique) 🔍
*Prerequisite: Stealth Level 1*
*Historical Source: v4 Previous Technique*

**Concept:** City-based stealth and navigation

**Historical Mechanics:**
- Use crowds as cover for stealth
- No penalties for urban obstacle movement
- Social camouflage in populated areas

**v4 Adaptation Needed:**
- Define crowd mechanics and cover bonuses
- Specify urban movement advantages
- Integrate social and stealth elements

### Social Specialist Support

#### **Silver Tongue** (Technique) 🔍
*Prerequisite: Influence Level 1*
*Historical Source: v4 Previous Technique*

**Concept:** Enhanced persuasion and social manipulation

**Historical Mechanics:**
- Add target's emotional state as bonus dice
- Fast talk without preparation
- Inspiring presence for allies

**v4 Adaptation Needed:**
- Define emotional state mechanics
- Specify Will costs for enhanced persuasion
- Clarify ally inspiration effects

#### **Intimidating Presence** (Technique) 🔍
*Prerequisite: Influence Level 1*
*Historical Source: v4 Previous Technique*

**Concept:** Fear-based social control and battlefield presence

**Historical Mechanics:**
- Use Physique for intimidation
- Successful attacks force morale checks
- Reputation effects in social encounters

**v4 Adaptation Needed:**
- Define intimidation mechanics and costs
- Specify morale check triggers
- Balance fear effects vs. other social approaches

---

## Priority 3: Combat Specialization

### Weapon Mastery

#### **Duelist** (Technique) 🔍
*Prerequisite: Light Melee Level 1*
*Historical Source: v2/v3 Weapons Craft Style*

**Concept:** Finesse weapon specialization for one-on-one combat

**Historical Mechanics:**
- Efficient parrying with Stamina instead of AP
- Riposte counterattacks after successful parries
- Defensive bonuses against single opponents

#### **Two-Weapon Fighting** (Technique) 🔍
*Prerequisite: Light Melee Level 1*
*Historical Source: v2/v3 Weapons Craft Specialization*

**Concept:** Dual wielding combat techniques

**Historical Mechanics:**
- Combine equipment stats of both weapons
- Enhanced Stunts when using paired weapons
- Reduced penalties for off-hand attacks

#### **Marksman** (Technique) 🔍
*Prerequisite: Ranged Level 1*
*Historical Source: v2/v3 Ranged Combat Style*

**Concept:** Precision ranged combat specialization

**Historical Mechanics:**
- Reduce Complexity for aimed shots
- Enhanced Target Weakness Stunt effects
- Overcome obstacles and armor

### Animal Handler Support

#### **Beast Tamer** (Technique) 🔍
*Prerequisite: Influence Level 1*
*Historical Source: v2/v3 Husbandry Specialization*

**Concept:** Animal training and control

**Historical Mechanics:**
- Training animals for specific tasks
- Enhanced animal communication
- Coordinated actions with trained animals

#### **Cavalry Tactics** (Technique) 🔍
*Prerequisite: Beast Tamer*
*Historical Source: v2/v3 Husbandry Specialization*

**Concept:** Mounted combat coordination

**Historical Mechanics:**
- Enhanced mounted combat formations
- Coordinated charges and maneuvers
- Mount and rider acting as single unit

## Next Steps

1. **Review Priority 1 techniques** for v4 compatibility and balance
2. **Develop detailed mechanics** for approved techniques
3. **Playtest integration** with existing v4 systems
4. **Refine and finalize** before adding to main README
5. **Continue with Priority 2 and 3** techniques
