# Monsters & Creatures

*Domain-specific guide for SellSword v4 creature design, statistics, and implementation*

## Table of Contents

1. [Design Philosophy](#design-philosophy)
2. [Monster Structure](#monster-structure)
3. [Core Statistics](#core-statistics)
4. [Creating New Monsters](#creating-new-monsters)
5. [Monster Categories](#monster-categories)
6. [Advanced Mechanics](#advanced-mechanics)
7. [Universal Monstrous Traits](#universal-monstrous-traits)
8. [Design Guidelines](#design-guidelines)
9. [Cross-References](#cross-references)

---

## Design Philosophy

Monsters should feel genuinely **monstrous** and distinct from player characters:

**Superhuman Capabilities:** Often exceed normal limits in strength, speed, resilience, or senses.

**Unique Mechanics:** Possess traits that interact with or bypass standard rules in interesting ways.

**Clear Identity:** Each creature has a defined role (ambusher, brute, controller) reinforced by mechanics.

**Manageable Complexity:** Prioritize impactful abilities over numerous minor traits for ease of GM use.

**Narrative Purpose:** Every creature serves a clear role in adventures while presenting meaningful tactical choices.

---

## Monster Structure

All monsters use a consistent template structure (see `_template_monster_v4.md`):

**Metadata:** Name, descriptions, size, type, and tags for player-facing information

**Core Stats:** Combat pool, action points, speed, and wound thresholds

**Defense:** Target difficulty, damage reduction, and special resistances

**Actions:** All AP-costing abilities with reach, damage, pierce, force, and extra success effects

**Monstrous Traits:** Passive abilities and unique rule interactions

**Behavior:** Tactics, loot, and typical environments

### Weaknesses & Vulnerabilities

Monsters have defined weaknesses tied to Action Scores (Physique, Finesse, Precision, Focus, Insight):

**Weaknesses:** Minor hindrances (-2 dice to combat_pool)
**Vulnerabilities:** Significant flaws (-4 dice to combat_pool)

Every monster should have at least one weakness or vulnerability to provide tactical options.

---

## Core Statistics

### Size Categories & Correlations
- **Tiny:** Cat-sized (higher TD, lower wounds/DR)
- **Small:** Goblin-sized (balanced stats)
- **Medium:** Human-sized (baseline)
- **Large:** Bear-sized (more wounds/DR, lower TD)
- **Huge:** Giant-sized (high wounds/DR, very low TD)
- **Gargantuan:** Kraken-sized (massive wounds/DR, environmental threat)

### Stat Guidelines

**Combat Pool (Base d6s):**
- Minion: 2-4d6 | Standard: 4-6d6 | Elite: 6-8d6 | Boss: 8-10+d6

**Success Probability Chart:**
| Pool | 1+ Success | 2+ Successes | 3+ Successes |
|------|------------|--------------|--------------|
| 3d6  | 42%        | 7%           | 1%           |
| 4d6  | 52%        | 13%          | 2%           |
| 5d6  | 60%        | 20%          | 4%           |
| 6d6  | 67%        | 26%          | 6%           |
| 7d6  | 72%        | 33%          | 10%          |
| 8d6  | 77%        | 40%          | 13%          |
| 9d6  | 81%        | 46%          | 18%          |
| 10d6 | 84%        | 52%          | 22%          |

**Action Points (PCs have 4):**
- Slow: 2 AP | Standard: 3 AP | Agile: 4 AP | Boss: 5+ AP

**Speed (Hexes per AP, Human = 4):**
- Slow: 2-3 | Average: 4 | Fast: 5-6 | Swift: 7+
- Special movement: `4 (Climb 4, Fly 6)`

**Wounds (Tiered resilience pools):**
- Fragile: 1-2 Minor | Standard: 3-4 total | Tough: 4-6 total | Very Tough: 6+ total

**Target Difficulty (Difficulty Dice to attackers):**
- Easy: TD 0 | Average: TD 1 | Agile: TD 2 | Very Hard: TD 3+

**Damage Reduction:**
- Unarmored: DR 0 | Light: DR 1-2 | Medium: DR 3-4 | Heavy: DR 5+

### Damage Scale

**Monster Damage Levels (Players use 1-3 scale):**
- Level 1-2: Minor/Moderate | Level 3-4: Serious/Severe | Level 5-6: Extreme/Devastating

**Pierce:** Reduces target's effective DR before damage calculation
**Force:** Triggers knockback, stagger, or prone effects

---

## Creating New Monsters

### Design Process

1. **Concept & Theme:** Define the creature's core identity and role
2. **Assign Stats:** Use size correlations and role guidelines
3. **Develop Traits:** Create 1-3 key abilities that feel "truly monstrous"
4. **Define Actions:** Standard attacks and special maneuvers
5. **Review:** Ensure it's distinct, challenging, and manageable

### Quality Checklist

**Mechanics:** Complete stat block, clear abilities, balanced challenge
**Narrative:** Clear adventure role, consistent lore, interesting behavior

---

## Monster Categories

**Fae:** Mystical creatures operating by strange rules, often interacting with emotions and promises.

**Aetherborn:** Creatures mutated by Aetheric presence since the Great Vanishing, with unpredictable magical abilities.

**Primordials:** Ancient beings tied to raw natural forces, predating the Garden Born civilizations.

---

## Advanced Mechanics

### Overwhelm Effects

Powerful effects triggered when any action achieves **3+ successes** (no exceptions). More impactful than standard extra success effects and should feel thematic to the creature.

**Format:** `[Effect Name] (Overwhelm Effect)`

**Examples:**
- **Shadow Swarm:** All other goblins within 4 hexes move 1 hex toward target (pack coordination)
- **Cruel Command:** All fae allies within 6 hexes gain Upper Hand next action (tactical leadership)
- **Ground Shaker:** All creatures within 3 hexes make Physique check or fall prone (devastating impact)

### Limiting Powerful Abilities

**Conditional:** Only usable under specific conditions (*teleport in shadows*)
**Backlash:** Causes negative effects on the monster (*energy blast self-damage*)
**Charge-Up:** Requires preparatory actions (*dragon breath needs "inhale"*)
**Recharge:** Available after achieving successes on other actions

### Reach & Loot

**Reach = Base (by Size) + Weapon Reach**
- Tiny/Small: 0 | Medium: 1 | Large: 2 | Huge: 3 | Gargantuan: 4

**Loot Guidelines:**
- Anatomical parts: List without dice (*tusks, hide*)
- Crafting materials: Note potential (*"Hide craftable into DR 2 armor"*)
- Coins/treasure: Use d6 notation (*"2d6 Silver coins"*)

---

## Universal Monstrous Traits

Common traits that can be applied to multiple creature types. Include the full description in each monster file for GM reference.

### **Cowardly**
When reduced to 1 wound (bloodied) or when outnumbered in melee range, the creature must make a Focus check or immediately flee using available movement abilities, spending all remaining AP moving away from enemies.

### **Pack Hunter**
When attacking the same target as an ally within 2 hexes, gains Upper Hand. Does not stack with other sources of Upper Hand.

### **Keen Senses**
Cannot be surprised and automatically detects hidden enemies within 8 hexes. Ignores penalties from dim light or darkness when making Focus checks.

### **Fae Cunning**
Can sense the emotional state of creatures within 5 hexes and gains +1 die to combat_pool when attacking frightened, confused, or wounded targets.

---

## Design Guidelines

### **Ability Ranges**
Consider practical GM table use when setting ranges (1 hex ≈ 1 yard):
- **1-2 hexes:** Melee/touch range abilities
- **3-5 hexes:** Close tactical abilities and coordination
- **5-10 hexes:** Sensory abilities (smell, hearing, supernatural senses)
- **10+ hexes:** Long-range effects and environmental abilities

### **Environmental Limitations**
Use meaningful restrictions rather than arbitrary cooldowns:
- **Bright light** negates shadow/stealth abilities
- **Line of sight** requirements for teleportation (horror theme)
- **Terrain types** for movement or hiding abilities

### **Group Synergy**
Design abilities that scale with multiple creatures:
- **Scaling Effects:** Difficulty increases per additional creature (+1 per goblin)
- **Coordination Bonuses:** Shared benefits within range
- **Psychological Pressure:** Multiple sources amplify fear effects

### **Player Interactions**
- Use **15 Skills** for player checks, not Action Scores
- Apply **"Compromised (-2 dice)"** not "+1 Difficulty"
- **Resolve checks** for fear/psychological effects

### **Teleportation Actions**
- Link distance to **Speed stat** for consistency
- Require **environmental conditions** (shadows, darkness)
- Add **line of sight restrictions** for horror themes
- Consider **1 AP cost** for tactical use

### **Dice Pool Coordination**
- **Some creatures pool dice** - goblins almost always attack in coordinated groups
- **Design for groups** - Overwhelm Effects should account for combined attacks
- **Individual vs Pack** - consider how the creature actually fights when setting thresholds

### **Cognitive Load Management**
- **Avoid complex tracking** - prefer binary states over stacking bonuses
- **Use existing mechanics** - Upper Hand instead of "+1 die to combat_pool"
- **GM table management** - abilities should be easy to adjudicate during play
- **Limit active effects** - too many buffs/debuffs become unwieldy

---

## Cross-References

### Related Systems
- **Combat Mechanics:** See [Combat README](../Combat/README.md) for action resolution and damage
- **Equipment:** See [Equipment README](../Equipment/README.md) for weapon and armor interactions
- **Magic System:** See [Magic README](../Magic/README.md) for magical creature abilities
- **Character Creation:** See [Characters README](../Characters/README.md) for comparison baselines

### Monster Resources
- **Creation Template:** [_template_monster_v4.md](_template_monster_v4.md)
- **Example Creatures:** [Huldrekall_v4.md](Huldrekall_v4.md) and other individual monster files
- **Style Guide:** [SellSword_v4_Style_Guide.md](../SellSword_v4_Style_Guide.md)
- **Current Priorities:** [SellSword_v4_TODO.md](../SellSword_v4_TODO.md)

---

*Monsters in SellSword should feel genuinely monstrous while serving clear narrative and mechanical purposes. Each creature should present unique challenges that force players to adapt their tactics.*

**Version:** 4.0
**Last Updated:** December 15, 2024
